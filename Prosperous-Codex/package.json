{"name": "prosperous-codex", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:integration": "jest --testPathPattern=integration", "test:unit": "jest --testPathPattern=unit", "test:api": "jest --testPathPattern=api", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "init-db": "node scripts/dev/init-db.js", "reset-db": "rm -f data/current/prosperous-codex.db && npm run init-db", "reset-auth": "node scripts/dev/reset-auth.js", "docs:generate": "node scripts/docs/generate-docs.js", "docs:serve": "npx http-server docs -p 8080 -o", "validate:schema": "node scripts/validation/validate-schema.js", "validate:field-mappings": "node scripts/validation/validate-field-mappings.js", "validate:naming-conventions": "node scripts/validation/validate-naming.js", "validate:all": "npm run validate:schema && npm run validate:field-mappings && npm run validate:naming-conventions", "cleanup:post-debug": "tsx scripts/maintenance/cleanup-post-debugging.ts", "audit:field-mapping": "tsx scripts/maintenance/audit-field-mapping.ts", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,md}\""}, "dependencies": {"@formatjs/fast-memoize": "^2.2.7", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@types/better-sqlite3": "^7.6.13", "bcryptjs": "^3.0.2", "better-sqlite3": "^11.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "isomorphic-dompurify": "^2.25.0", "jsdom": "^26.1.0", "lightningcss": "^1.30.1", "lucide-react": "^0.511.0", "next": "^15.3.3", "next-auth": "^5.0.0-beta.28", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "openai": "^5.8.2", "parse5": "^7.3.0", "react": "^19.1.0", "react-confetti-explosion": "^3.0.3", "react-day-picker": "^9.7.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.51"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "node-mocks-http": "^1.17.2", "tailwindcss": "^4", "ts-jest": "^29.3.4", "tw-animate-css": "^1.3.3", "typescript": "^5"}}