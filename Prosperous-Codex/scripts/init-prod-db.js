#!/usr/bin/env node

/**
 * Production Database Initialization Script
 * 
 * This script initializes the PostgreSQL database for production deployment
 * It creates the schema and default users
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
const bcrypt = require('bcryptjs');

// Configuration
const POSTGRES_URL = process.env.POSTGRES_URL;
const SCHEMA_PATH = path.join(__dirname, '..', 'src', 'lib', 'database', 'postgres-schema.sql');

if (!POSTGRES_URL) {
  console.error('❌ POSTGRES_URL environment variable is required');
  process.exit(1);
}

if (!fs.existsSync(SCHEMA_PATH)) {
  console.error(`❌ Schema file not found at: ${SCHEMA_PATH}`);
  process.exit(1);
}

// Initialize PostgreSQL connection
const pool = new Pool({
  connectionString: POSTGRES_URL,
  ssl: { rejectUnauthorized: false },
  max: 5,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 10000,
});

async function createSchema() {
  console.log('📋 Creating database schema...');
  
  const client = await pool.connect();
  
  try {
    // Read and execute schema
    const schema = fs.readFileSync(SCHEMA_PATH, 'utf-8');
    await client.query(schema);
    
    console.log('   ✅ Schema created successfully');
  } catch (error) {
    if (error.message.includes('already exists')) {
      console.log('   ✅ Schema already exists, skipping creation');
    } else {
      throw error;
    }
  } finally {
    client.release();
  }
}

async function createDefaultUsers() {
  console.log('👥 Creating default users...');
  
  const client = await pool.connect();
  
  try {
    // Check if admin user exists
    const adminCheck = await client.query(
      'SELECT id FROM users WHERE email = $1',
      ['<EMAIL>']
    );
    
    if (adminCheck.rows.length === 0) {
      // Create admin user
      const adminPasswordHash = await bcrypt.hash('password', 12);
      await client.query(`
        INSERT INTO users (email, username, password_hash, role)
        VALUES ($1, $2, $3, $4)
      `, ['<EMAIL>', 'admin', adminPasswordHash, 'admin']);
      
      console.log('   ✅ Admin user created (<EMAIL> / password)');
      console.log('   ⚠️  IMPORTANT: Change the admin password after first login!');
    } else {
      console.log('   ✅ Admin user already exists');
    }
    
    // Check if moderator user exists
    const moderatorCheck = await client.query(
      'SELECT id FROM users WHERE email = $1',
      ['<EMAIL>']
    );
    
    if (moderatorCheck.rows.length === 0) {
      // Create moderator user
      const moderatorPasswordHash = await bcrypt.hash('moderator123', 12);
      await client.query(`
        INSERT INTO users (email, username, password_hash, role)
        VALUES ($1, $2, $3, $4)
      `, ['<EMAIL>', 'moderator', moderatorPasswordHash, 'moderator']);
      
      console.log('   ✅ Moderator user created (<EMAIL> / moderator123)');
      console.log('   ⚠️  IMPORTANT: Change the moderator password after first login!');
    } else {
      console.log('   ✅ Moderator user already exists');
    }
    
  } finally {
    client.release();
  }
}

async function validateDatabase() {
  console.log('🔍 Validating database...');
  
  const client = await pool.connect();
  
  try {
    // Check if all required tables exist
    const requiredTables = [
      'users', 'projects', 'tasks', 'project_team_members',
      'project_files', 'project_comments', 'activity_log',
      'user_paper_options', 'saved_calculations'
    ];
    
    for (const tableName of requiredTables) {
      const result = await client.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = $1
      `, [tableName]);
      
      if (result.rows.length > 0) {
        console.log(`   ✅ Table ${tableName} exists`);
      } else {
        console.log(`   ❌ Table ${tableName} missing`);
        throw new Error(`Required table ${tableName} is missing`);
      }
    }
    
    // Check user count
    const userCount = await client.query('SELECT COUNT(*) as count FROM users');
    console.log(`   ✅ Users in database: ${userCount.rows[0].count}`);
    
    // Test basic functionality
    await client.query('SELECT 1');
    console.log('   ✅ Database connection and queries working');
    
  } finally {
    client.release();
  }
}

async function main() {
  console.log('🚀 Initializing production database...');
  console.log(`🐘 PostgreSQL: ${POSTGRES_URL.replace(/\/\/.*@/, '//***@')}`);
  
  try {
    // Test connection
    console.log('🔌 Testing database connection...');
    const client = await pool.connect();
    await client.query('SELECT 1');
    client.release();
    console.log('   ✅ Database connection OK');
    
    // Create schema
    await createSchema();
    
    // Create default users
    await createDefaultUsers();
    
    // Validate database
    await validateDatabase();
    
    console.log('✅ Production database initialization completed successfully!');
    console.log('');
    console.log('🔐 Default Login Credentials:');
    console.log('   Admin: <EMAIL> / password');
    console.log('   Moderator: <EMAIL> / moderator123');
    console.log('');
    console.log('⚠️  SECURITY WARNING: Change default passwords immediately after deployment!');
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
Production Database Initialization Script

Usage:
  node scripts/init-prod-db.js

Environment Variables:
  POSTGRES_URL      PostgreSQL connection string (required)
  NODE_ENV          Set to 'production' for SSL connections

Example:
  POSTGRES_URL="postgresql://user:pass@localhost/dbname" node scripts/init-prod-db.js
  `);
  process.exit(0);
}

// Run initialization
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { createSchema, createDefaultUsers, validateDatabase };
