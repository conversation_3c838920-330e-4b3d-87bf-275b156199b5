#!/usr/bin/env node

/**
 * SQLite to PostgreSQL Migration Script
 * 
 * This script migrates data from SQLite to PostgreSQL for Prosperous Codex
 * Run this script after setting up your PostgreSQL database
 */

const Database = require('better-sqlite3');
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Configuration
const SQLITE_DB_PATH = process.env.SQLITE_DB_PATH || path.join(process.cwd(), 'data', 'prosperous-codex.db');
const POSTGRES_URL = process.env.POSTGRES_URL;

if (!POSTGRES_URL) {
  console.error('❌ POSTGRES_URL environment variable is required');
  process.exit(1);
}

if (!fs.existsSync(SQLITE_DB_PATH)) {
  console.error(`❌ SQLite database not found at: ${SQLITE_DB_PATH}`);
  process.exit(1);
}

// Initialize connections
const sqliteDb = new Database(SQLITE_DB_PATH, { readonly: true });
const pgPool = new Pool({
  connectionString: POSTGRES_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

// Tables to migrate (in dependency order)
const TABLES = [
  'users',
  'user_sessions',
  'access_requests',
  'projects',
  'tasks',
  'project_team_members',
  'project_files',
  'project_comments',
  'project_tags',
  'activity_log',
  'project_edit_history',
  'user_paper_options',
  'saved_calculations',
  'calculation_history'
];

// Column mappings for data type conversions
const COLUMN_MAPPINGS = {
  users: {
    is_active: (value) => value === 1 ? true : false
  },
  user_paper_options: {
    is_custom: (value) => value === 1 ? true : false
  }
};

async function migrateTable(tableName) {
  console.log(`📦 Migrating table: ${tableName}`);
  
  try {
    // Get SQLite data
    const sqliteData = sqliteDb.prepare(`SELECT * FROM ${tableName}`).all();
    
    if (sqliteData.length === 0) {
      console.log(`   ✅ Table ${tableName} is empty, skipping`);
      return;
    }

    // Get PostgreSQL client
    const client = await pgPool.connect();
    
    try {
      // Clear existing data (be careful in production!)
      await client.query(`DELETE FROM ${tableName}`);
      
      // Get column names from first row
      const columns = Object.keys(sqliteData[0]);
      const placeholders = columns.map((_, index) => `$${index + 1}`).join(', ');
      const columnNames = columns.join(', ');
      
      // Prepare insert statement
      const insertQuery = `
        INSERT INTO ${tableName} (${columnNames})
        VALUES (${placeholders})
      `;
      
      // Insert data row by row
      for (const row of sqliteData) {
        const values = columns.map(col => {
          let value = row[col];
          
          // Apply column mappings if they exist
          if (COLUMN_MAPPINGS[tableName] && COLUMN_MAPPINGS[tableName][col]) {
            value = COLUMN_MAPPINGS[tableName][col](value);
          }
          
          return value;
        });
        
        await client.query(insertQuery, values);
      }
      
      // Reset sequence for auto-increment columns
      if (columns.includes('id')) {
        const maxIdResult = await client.query(`SELECT MAX(id) as max_id FROM ${tableName}`);
        const maxId = maxIdResult.rows[0].max_id || 0;
        
        if (maxId > 0) {
          await client.query(`SELECT setval(pg_get_serial_sequence('${tableName}', 'id'), ${maxId})`);
        }
      }
      
      console.log(`   ✅ Migrated ${sqliteData.length} rows to ${tableName}`);
      
    } finally {
      client.release();
    }
    
  } catch (error) {
    console.error(`   ❌ Error migrating ${tableName}:`, error.message);
    throw error;
  }
}

async function validateMigration() {
  console.log('🔍 Validating migration...');
  
  const client = await pgPool.connect();
  
  try {
    for (const tableName of TABLES) {
      // Count rows in SQLite
      const sqliteCount = sqliteDb.prepare(`SELECT COUNT(*) as count FROM ${tableName}`).get().count;
      
      // Count rows in PostgreSQL
      const pgResult = await client.query(`SELECT COUNT(*) as count FROM ${tableName}`);
      const pgCount = parseInt(pgResult.rows[0].count);
      
      if (sqliteCount === pgCount) {
        console.log(`   ✅ ${tableName}: ${sqliteCount} rows (match)`);
      } else {
        console.log(`   ❌ ${tableName}: SQLite=${sqliteCount}, PostgreSQL=${pgCount} (mismatch)`);
      }
    }
  } finally {
    client.release();
  }
}

async function main() {
  console.log('🚀 Starting SQLite to PostgreSQL migration...');
  console.log(`📂 SQLite DB: ${SQLITE_DB_PATH}`);
  console.log(`🐘 PostgreSQL: ${POSTGRES_URL.replace(/\/\/.*@/, '//***@')}`);
  
  try {
    // Test connections
    console.log('🔌 Testing connections...');
    
    // Test SQLite
    const sqliteTest = sqliteDb.prepare('SELECT 1').get();
    console.log('   ✅ SQLite connection OK');
    
    // Test PostgreSQL
    const client = await pgPool.connect();
    await client.query('SELECT 1');
    client.release();
    console.log('   ✅ PostgreSQL connection OK');
    
    // Migrate tables
    console.log('📦 Starting table migration...');
    for (const tableName of TABLES) {
      await migrateTable(tableName);
    }
    
    // Validate migration
    await validateMigration();
    
    console.log('✅ Migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    // Close connections
    sqliteDb.close();
    await pgPool.end();
  }
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
SQLite to PostgreSQL Migration Script

Usage:
  node scripts/migrate-sqlite-to-postgres.js

Environment Variables:
  SQLITE_DB_PATH    Path to SQLite database (default: ./data/prosperous-codex.db)
  POSTGRES_URL      PostgreSQL connection string (required)

Example:
  POSTGRES_URL="postgresql://user:pass@localhost/dbname" node scripts/migrate-sqlite-to-postgres.js
  `);
  process.exit(0);
}

// Run migration
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { migrateTable, validateMigration };
