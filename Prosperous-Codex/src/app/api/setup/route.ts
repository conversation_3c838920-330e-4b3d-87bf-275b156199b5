import { NextResponse } from 'next/server';
import { initializeApp, getDatabaseStatus } from '@/lib/database/init';

/**
 * Public endpoint for first-time database setup
 * This endpoint allows initialization only if no users exist in the database
 */
export async function POST() {
  try {
    console.log('🚀 Setup endpoint called');
    
    // Check current database status
    const status = await getDatabaseStatus();
    console.log('📊 Database status:', status);

    // Only allow setup if database is not healthy or has no users
    if (status.healthy && status.stats && status.stats.users > 0) {
      return NextResponse.json(
        { 
          error: 'Database is already initialized with users. Use admin endpoints for re-initialization.',
          status: status
        },
        { status: 409 } // Conflict
      );
    }
    
    console.log('🔧 Initializing database...');
    const result = await initializeApp();
    
    if (result.success) {
      const newStatus = await getDatabaseStatus();
      console.log('✅ Database initialization successful');
      
      return NextResponse.json({ 
        message: 'Database initialized successfully! You can now login with the default credentials.',
        credentials: {
          admin: '<EMAIL> / password',
          moderator: '<EMAIL> / moderator123',
          user: '<EMAIL> / user123'
        },
        status: newStatus
      });
    } else {
      console.error('❌ Database initialization failed:', result.error);
      return NextResponse.json(
        {
          error: 'Database initialization failed',
          details: result.error,
          status: await getDatabaseStatus()
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('❌ Setup endpoint error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error during setup',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint to check if setup is needed
 */
export async function GET() {
  try {
    const status = await getDatabaseStatus();

    return NextResponse.json({
      setupNeeded: !status.healthy || (status.stats && status.stats.users === 0),
      status: status
    });
  } catch (error) {
    console.error('Setup status check error:', error);
    return NextResponse.json(
      {
        error: 'Failed to check setup status',
        setupNeeded: true // Assume setup is needed if we can't check
      },
      { status: 500 }
    );
  }
}
