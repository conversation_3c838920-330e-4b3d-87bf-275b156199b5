import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const url = new URL(request.url);
  const testType = url.searchParams.get('test') || 'basic';

  if (testType === 'create') {
    // Test project creation via GET request for easy browser testing
    try {
      console.log('🧪 Testing TaskMaster project creation (via GET)...');

      const { SimpleTaskMasterService } = await import('@/lib/database/simple-task-master-service');
      const taskMasterService = new SimpleTaskMasterService();

      const testProjectData = {
        title: 'Browser Test Project',
        description: 'Test project created via browser GET request',
        fullDescription: 'A test project created to debug TaskMaster functionality',
        eventLog: 'Browser test project created',
        status: 'todo' as const,
        priority: 'medium' as const,
        visibility: 'public' as const
      };

      console.log('📝 Test project data:', JSON.stringify(testProjectData, null, 2));
      console.log('🔨 Calling createProject with userId: 1');

      const project = await taskMasterService.createProject(1, testProjectData);

      console.log('✅ Project created successfully:', project);

      return NextResponse.json({
        success: true,
        message: 'Test project created successfully via GET',
        project: {
          id: project.id,
          title: project.title,
          status: project.status
        }
      });

    } catch (error) {
      console.error('❌ Test project creation failed:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        name: error instanceof Error ? error.name : undefined,
        error: error
      });

      return NextResponse.json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        details: error instanceof Error ? error.stack : undefined
      }, { status: 500 });
    }
  }

  // Default basic test
  try {
    console.log('🧪 Testing TaskMaster service instantiation...');

    // Test 1: Basic response
    console.log('✅ Basic endpoint working');

    // Test 2: Try importing SimpleTaskMasterService
    console.log('📦 Importing SimpleTaskMasterService...');
    const { SimpleTaskMasterService } = await import('@/lib/database/simple-task-master-service');
    console.log('✅ SimpleTaskMasterService imported successfully');

    // Test 3: Try instantiating SimpleTaskMasterService
    console.log('🔨 Instantiating SimpleTaskMasterService...');
    const taskMasterService = new SimpleTaskMasterService();
    console.log('✅ SimpleTaskMasterService instantiated successfully');

    // Test 4: Try a simple method call (if available)
    console.log('🔍 Testing basic method...');
    // Just test if the service has expected methods
    const hasCreateProject = typeof taskMasterService.createProject === 'function';
    const hasGetProjects = typeof taskMasterService.getProjects === 'function';
    console.log('📋 Method availability:', { hasCreateProject, hasGetProjects });

    return NextResponse.json({
      success: true,
      message: 'TaskMaster service test successful',
      tests: {
        basicEndpoint: true,
        importSuccess: true,
        instantiationSuccess: true,
        methodAvailability: { hasCreateProject, hasGetProjects }
      },
      instructions: 'Add ?test=create to URL to test project creation'
    });

  } catch (error) {
    console.error('❌ TaskMaster service test failed:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : undefined,
      error: error
    });

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing TaskMaster project creation (minimal)...');
    
    // Test basic project creation without middleware
    const { SimpleTaskMasterService } = await import('@/lib/database/simple-task-master-service');
    const taskMasterService = new SimpleTaskMasterService();

    const testProjectData = {
      title: 'Test Project',
      description: 'Simple test project',
      fullDescription: 'A simple test project for debugging',
      eventLog: 'Test project created',
      status: 'todo' as const,
      priority: 'medium' as const,
      visibility: 'public' as const
    };
    
    console.log('📝 Test project data:', JSON.stringify(testProjectData, null, 2));
    console.log('🔨 Calling createProject with userId: 1');
    
    const project = await taskMasterService.createProject(1, testProjectData);
    
    console.log('✅ Project created successfully:', project);
    
    return NextResponse.json({
      success: true,
      message: 'Test project created successfully',
      project: {
        id: project.id,
        title: project.title,
        status: project.status
      }
    });
    
  } catch (error) {
    console.error('❌ Test project creation failed:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : undefined,
      error: error
    });
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
