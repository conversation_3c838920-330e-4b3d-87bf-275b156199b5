import { NextRequest, NextResponse } from 'next/server';
import { TaskMasterService } from '@/lib/database/postgres-task-master-service';
import { requireAuth } from '@/lib/auth/middleware';
import { readFile } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

export async function GET(
  request: NextRequest,
  { params }: { params: { fileId: string } }
) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { user } = authResult;
    const fileId = parseInt(params.fileId);
    
    if (isNaN(fileId)) {
      return NextResponse.json(
        { error: 'Invalid file ID' },
        { status: 400 }
      );
    }
    
    const taskMasterService = new TaskMasterService();
    
    // Get file info
    const file = taskMasterService.getFileById(fileId);
    
    if (!file) {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }
    
    // Check if user has access to this project
    const userProjects = taskMasterService.getUserProjects(parseInt(user.id));
    const hasAccess = userProjects.some(p => p.id === file.project_id);
    
    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }
    
    // Construct file path
    const filePath = join(process.cwd(), 'data', file.file_path);
    
    if (!existsSync(filePath)) {
      return NextResponse.json(
        { error: 'File not found on disk' },
        { status: 404 }
      );
    }
    
    // Read file
    const fileBuffer = await readFile(filePath);
    
    // Set appropriate headers
    const headers = new Headers();
    headers.set('Content-Type', file.file_type || 'application/octet-stream');
    headers.set('Content-Disposition', `attachment; filename="${file.file_name}"`);
    headers.set('Content-Length', fileBuffer.length.toString());
    
    return new NextResponse(fileBuffer, {
      status: 200,
      headers
    });
    
  } catch (error) {
    console.error('Download file error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
