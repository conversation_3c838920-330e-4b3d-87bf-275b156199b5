import { NextRequest } from 'next/server';
import { z } from 'zod';
import { TaskMasterService } from '@/lib/database/postgres-task-master-service';
import {
  withTaskMasterMiddleware,
  createApiResponse,
  createErrorResponse,
  ApiContext
} from '@/lib/task-master/api-middleware';
import { ProjectSchemas } from '@/lib/task-master/schemas';
import { FieldMapper } from '@/lib/task-master/field-mapping';

export const GET = withTaskMasterMiddleware(
  async (request: NextRequest, context: ApiContext) => {
    try {
      const { user, query } = context;

      // Parse query parameters with defaults
      const page = query?.page ? parseInt(query.page as string) : 1;
      const limit = Math.min(query?.limit ? parseInt(query.limit as string) : 20, 100);
      const offset = (page - 1) * limit;
      const status = query?.status as string || undefined;
      const includeDetails = query?.includeDetails === 'true';

      const taskMasterService = new TaskMasterService();

      // Get projects with pagination and optional details
      const userId = parseInt(user.id);
      const allProjects = await taskMasterService.getProjects(userId, includeDetails);

      // Apply status filter if provided
      let filteredProjects = allProjects;
      if (status) {
        filteredProjects = allProjects.filter(p => p.status === status);
      }

      // Apply pagination
      const projects = filteredProjects.slice(offset, offset + limit);

      // Get total count for pagination metadata
      const totalCount = await taskMasterService.getProjectCount(userId, status);
      const totalPages = Math.ceil(totalCount / limit);

      return createApiResponse({
        projects,
        pagination: {
          page,
          limit,
          offset,
          totalCount,
          totalPages,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1
        },
        filters: {
          status
        }
      });

    } catch (error) {
      console.error('Get projects error:', error);
      return createErrorResponse(error, context.requestId);
    }
  },
  {
    requireAuth: true,
    sanitizeInput: true,
    validateQuery: z.object({
      page: z.string().optional(),
      limit: z.string().optional(),
      status: z.enum(['todo', 'inProgress', 'completed']).optional(),
      includeDetails: z.string().optional()
    }).optional()
  }
);

export const POST = withTaskMasterMiddleware(
  async (request: NextRequest, context: ApiContext) => {
    try {
      const { user, body } = context;

      // Body is already validated by middleware using ProjectSchemas.create
      // Apply field mapping: camelCase (API) -> snake_case (Database)
      const projectData = FieldMapper.apiToDb(body);

      const taskMasterService = new TaskMasterService();

      // Create project with mapped data (now in snake_case for service layer)
      const project = await taskMasterService.createProject(parseInt(user.id), {
        title: projectData.title,
        description: projectData.description,
        full_description: projectData.full_description,
        status: projectData.status,
        priority: projectData.priority,
        progress: projectData.progress,
        due_date: projectData.due_date,
        assigned_to: projectData.assigned_to ? parseInt(projectData.assigned_to) : undefined,
        visibility: projectData.visibility,
        tags: projectData.tags || []
      });

      return createApiResponse(
        {
          message: 'Project created successfully',
          project
        },
        { status: 201 }
      );

    } catch (error) {
      console.error('Create project error:', error);
      return createErrorResponse(error, context.requestId);
    }
  },
  {
    requireAuth: true,
    sanitizeInput: true,
    validateBody: ProjectSchemas.create
  }
);
