import { NextRequest, NextResponse } from 'next/server';
import { TaskMasterService } from '@/lib/database/postgres-task-master-service';
import { requireAuth } from '@/lib/auth/middleware';

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string; commentId: string } }
) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { user } = authResult;
    const { id, commentId } = await params;
    const projectId = parseInt(id);
    const commentIdNum = parseInt(commentId);
    
    if (isNaN(projectId)) {
      return NextResponse.json(
        { error: 'Invalid project ID' },
        { status: 400 }
      );
    }
    
    if (isNaN(commentIdNum)) {
      return NextResponse.json(
        { error: 'Invalid comment ID' },
        { status: 400 }
      );
    }
    
    const { content } = await request.json();
    
    // Validate content
    if (!content || typeof content !== 'string' || content.trim().length === 0) {
      return NextResponse.json(
        { error: 'Comment content is required' },
        { status: 400 }
      );
    }
    
    if (content.length > 2000) {
      return NextResponse.json(
        { error: 'Comment content must be less than 2000 characters' },
        { status: 400 }
      );
    }
    
    const taskMasterService = new TaskMasterService();

    // Check if project exists
    const project = await taskMasterService.getProjectById(projectId);

    if (!project) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }
    
    // Update comment
    const updatedComment = await taskMasterService.updateComment(
      commentIdNum,
      user,
      content.trim()
    );
    
    return NextResponse.json({
      success: true,
      data: { comment: updatedComment },
      message: 'Comment updated successfully'
    });
    
  } catch (error) {
    console.error('Update comment error:', error);
    
    // Handle specific error types
    if (error && typeof error === 'object' && 'type' in error) {
      switch (error.type) {
        case 'NotFoundError':
          return NextResponse.json(
            { error: 'Comment not found' },
            { status: 404 }
          );
        case 'AuthorizationError':
          return NextResponse.json(
            { error: 'You can only edit your own comments' },
            { status: 403 }
          );
        case 'ValidationError':
          return NextResponse.json(
            { error: error.message || 'Invalid input' },
            { status: 400 }
          );
        default:
          return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
          );
      }
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string; commentId: string } }
) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { user } = authResult;
    const { id, commentId } = await params;
    const projectId = parseInt(id);
    const commentIdNum = parseInt(commentId);
    
    if (isNaN(projectId)) {
      return NextResponse.json(
        { error: 'Invalid project ID' },
        { status: 400 }
      );
    }
    
    if (isNaN(commentIdNum)) {
      return NextResponse.json(
        { error: 'Invalid comment ID' },
        { status: 400 }
      );
    }
    
    const taskMasterService = new TaskMasterService();
    
    // Check if user has access to this project
    const userProjects = taskMasterService.getUserProjects(parseInt(user.id));
    const hasAccess = userProjects.some(p => p.id === projectId);
    
    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }
    
    // Delete comment
    const success = await taskMasterService.deleteComment(commentIdNum, user);
    
    if (success) {
      return NextResponse.json({
        success: true,
        message: 'Comment deleted successfully'
      });
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to delete comment' },
        { status: 500 }
      );
    }
    
  } catch (error) {
    console.error('Delete comment error:', error);
    
    // Handle specific error types
    if (error && typeof error === 'object' && 'type' in error) {
      switch (error.type) {
        case 'NotFoundError':
          return NextResponse.json(
            { error: 'Comment not found' },
            { status: 404 }
          );
        case 'AuthorizationError':
          return NextResponse.json(
            { error: 'You can only delete your own comments' },
            { status: 403 }
          );
        default:
          return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
          );
      }
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
