import { NextRequest, NextResponse } from 'next/server';
import { TaskMasterService } from '@/lib/database/postgres-task-master-service';
import { requireAuth } from '@/lib/auth/middleware';
import { FieldMapper } from '@/lib/task-master/field-mapping';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);

    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const { user } = authResult;
    const { id } = await params;
    const projectId = parseInt(id);
    
    if (isNaN(projectId)) {
      return NextResponse.json(
        { error: 'Invalid project ID' },
        { status: 400 }
      );
    }

    // Get field name from query parameters
    const { searchParams } = new URL(request.url);
    const fieldName = searchParams.get('field');
    
    if (!fieldName || !['full_description', 'event_log'].includes(fieldName)) {
      return NextResponse.json(
        { error: 'Invalid field name. Must be full_description or event_log' },
        { status: 400 }
      );
    }
    
    const taskMasterService = new TaskMasterService();
    
    // Check if user has access to this project
    const userProjects = taskMasterService.getUserProjects(parseInt(user.id));
    const hasAccess = userProjects.some(p => p.id === projectId);
    
    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }
    
    // Get field edit history
    const editHistory = await taskMasterService.getProjectFieldEditHistory(
      projectId,
      fieldName as 'full_description' | 'event_log',
      user
    );

    // Apply field mapping to convert to camelCase for API response
    const mappedHistory = editHistory.map(item => FieldMapper.dbToApi(item));

    return NextResponse.json({
      success: true,
      data: {
        history: mappedHistory,
        count: mappedHistory.length
      },
      message: 'Project field edit history retrieved successfully'
    });

  } catch (error) {
    console.error('Get project field edit history error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
