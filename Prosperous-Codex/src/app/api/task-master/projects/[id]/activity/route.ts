import { NextRequest, NextResponse } from 'next/server';
import { TaskMasterService } from '@/lib/database/postgres-task-master-service';
import { requireAuth } from '@/lib/auth/middleware';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);

    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const { user } = authResult;
    const { id } = await params;
    const projectId = parseInt(id);
    
    if (isNaN(projectId)) {
      return NextResponse.json(
        { error: 'Invalid project ID' },
        { status: 400 }
      );
    }

    // Parse pagination parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const taskId = searchParams.get('taskId') ? parseInt(searchParams.get('taskId')!) : undefined;
    
    // Validate pagination parameters
    if (limit < 1 || limit > 100) {
      return NextResponse.json(
        { error: 'Limit must be between 1 and 100' },
        { status: 400 }
      );
    }
    
    if (offset < 0) {
      return NextResponse.json(
        { error: 'Offset must be non-negative' },
        { status: 400 }
      );
    }
    
    const taskMasterService = new TaskMasterService();
    
    // Check if user has access to this project
    const userProjects = taskMasterService.getUserProjects(parseInt(user.id));
    const hasAccess = userProjects.some(p => p.id === projectId);
    
    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }
    
    // Get paginated activity
    const activity = taskMasterService.getProjectActivity(projectId, {
      limit,
      offset,
      taskId
    });
    
    return NextResponse.json({ 
      activity,
      pagination: {
        limit,
        offset,
        hasMore: activity.length === limit // Simple check - if we got full page, there might be more
      }
    });
    
  } catch (error) {
    console.error('Get project activity error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
