import { NextRequest } from 'next/server';
import { TaskMasterService } from '@/lib/database/postgres-task-master-service';
import {
  withTaskMasterMiddleware,
  createApiResponse,
  createErrorResponse,
  ApiContext
} from '@/lib/task-master/api-middleware';
import { TaskSchemas } from '@/lib/task-master/schemas';
import { FieldMapper } from '@/lib/task-master/field-mapping';

export const GET = withTaskMasterMiddleware(
  async (request: NextRequest, context: ApiContext) => {
    try {
      const { user, params } = context;

      // Params are already validated by middleware using TaskSchemas.idParam
      const taskId = params!.taskId as number;

      const taskMasterService = new TaskMasterService();
      const task = await taskMasterService.getTaskById(taskId);

      if (!task) {
        return createApiResponse(undefined, {
          error: 'Task not found',
          status: 404,
          requestId: context.requestId
        });
      }

      // Check if user has access to this project
      const userProjects = await taskMasterService.getProjects(parseInt(user.id), false);
      const hasAccess = userProjects.some(p => p.id === task.projectId);

      if (!hasAccess) {
        return createApiResponse(undefined, {
          error: 'Access denied',
          status: 403,
          requestId: context.requestId
        });
      }

      return createApiResponse({ task });

    } catch (error) {
      console.error('Get task error:', error);
      return createErrorResponse(error, context.requestId);
    }
  },
  {
    requireAuth: true,
    sanitizeInput: true,
    validateParams: TaskSchemas.idParam
  }
);

export const PUT = withTaskMasterMiddleware(
  async (request: NextRequest, context: ApiContext) => {
    try {
      const { user, params, body } = context;

      // Params and body are already validated by middleware
      const taskId = params!.taskId as number;

      // Apply field mapping: camelCase (API) -> snake_case (Database)
      const updateData = FieldMapper.apiToDb(body);

      const taskMasterService = new TaskMasterService();
      const task = await taskMasterService.getTaskById(taskId);

      if (!task) {
        return createApiResponse(undefined, {
          error: 'Task not found',
          status: 404,
          requestId: context.requestId
        });
      }

      // Check if user has access to this project
      const userProjects = taskMasterService.getUserProjects(parseInt(user.id));
      const hasAccess = userProjects.some(p => p.id === task.projectId);

      if (!hasAccess) {
        return createApiResponse(undefined, {
          error: 'Access denied',
          status: 403,
          requestId: context.requestId
        });
      }

      // Update task
      const success = await taskMasterService.updateTask(taskId, user, updateData);

      if (!success) {
        return createApiResponse(undefined, {
          error: 'Failed to update task',
          status: 500,
          requestId: context.requestId
        });
      }

      // Return updated task
      const updatedTask = taskMasterService.getTaskById(taskId);
      return createApiResponse({ task: updatedTask });

    } catch (error) {
      console.error('Update task error:', error);
      return createErrorResponse(error, context.requestId);
    }
  },
  {
    requireAuth: true,
    sanitizeInput: true,
    validateParams: TaskSchemas.idParam,
    validateBody: TaskSchemas.update
  }
);

export const DELETE = withTaskMasterMiddleware(
  async (request: NextRequest, context: ApiContext) => {
    try {
      const { user, params } = context;

      // Params are already validated by middleware
      const taskId = params!.taskId as number;

      const taskMasterService = new TaskMasterService();
      const task = await taskMasterService.getTaskById(taskId);

      if (!task) {
        return createApiResponse(undefined, {
          error: 'Task not found',
          status: 404,
          requestId: context.requestId
        });
      }

      // Check if user has access to this project
      const userProjects = taskMasterService.getUserProjects(parseInt(user.id));
      const hasAccess = userProjects.some(p => p.id === task.projectId);

      if (!hasAccess) {
        return createApiResponse(undefined, {
          error: 'Access denied',
          status: 403,
          requestId: context.requestId
        });
      }

      // Delete task
      const success = await taskMasterService.deleteTask(taskId, user);

      if (!success) {
        return createApiResponse(undefined, {
          error: 'Failed to delete task',
          status: 500,
          requestId: context.requestId
        });
      }

      return createApiResponse({ success: true });

    } catch (error) {
      console.error('Delete task error:', error);
      return createErrorResponse(error, context.requestId);
    }
  },
  {
    requireAuth: true,
    sanitizeInput: true,
    validateParams: TaskSchemas.idParam
  }
);
