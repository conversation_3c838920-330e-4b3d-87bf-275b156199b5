import { NextRequest } from 'next/server';
import { TaskMasterService } from '@/lib/database/postgres-task-master-service';
import {
  withTaskMasterMiddleware,
  createApiResponse,
  createErrorResponse,
  ApiContext
} from '@/lib/task-master/api-middleware';
import { TaskSchemas, QuerySchemas } from '@/lib/task-master/schemas';

export const GET = withTaskMasterMiddleware(
  async (request: NextRequest, context: ApiContext) => {
    try {
      const { user, query } = context;

      // Query is already validated by middleware using QuerySchemas.taskListing
      // Query should be in camelCase (API layer standard)
      const projectId = query!.projectId;

      const taskMasterService = new TaskMasterService();

      // Check if user has access to this project
      const userProjects = await taskMasterService.getProjects(parseInt(user.id), false);
      const hasAccess = userProjects.some(p => p.id === projectId);

      if (!hasAccess) {
        return createApiResponse(undefined, {
          error: 'Access denied',
          status: 403,
          requestId: context.requestId
        });
      }

      // Get tasks for the project
      const tasks = await taskMasterService.getProjectTasks(projectId);

      return createApiResponse({ tasks });

    } catch (error) {
      console.error('Get tasks error:', error);
      return createErrorResponse(error, context.requestId);
    }
  },
  {
    requireAuth: true,
    sanitizeInput: true,
    validateQuery: QuerySchemas.taskListing
  }
);

export const POST = withTaskMasterMiddleware(
  async (request: NextRequest, context: ApiContext) => {
    try {
      const { user, body } = context;

      // Body is already validated by middleware using TaskSchemas.createWithProject
      // Body should be in camelCase (API layer standard)
      const { projectId, ...taskData } = body;

      const taskMasterService = new TaskMasterService();

      // Check if user has access to this project
      const userProjects = await taskMasterService.getProjects(parseInt(user.id), false);
      const hasAccess = userProjects.some(p => p.id === projectId);

      if (!hasAccess) {
        return createApiResponse(undefined, {
          error: 'Access denied',
          status: 403,
          requestId: context.requestId
        });
      }

      // Create task (service layer handles its own field mapping)
      const task = await taskMasterService.createTask(projectId, user, taskData);

      return createApiResponse({ task });

    } catch (error) {
      console.error('Create task error:', error);
      return createErrorResponse(error, context.requestId);
    }
  },
  {
    requireAuth: true,
    sanitizeInput: true,
    validateBody: TaskSchemas.createWithProject
  }
);
