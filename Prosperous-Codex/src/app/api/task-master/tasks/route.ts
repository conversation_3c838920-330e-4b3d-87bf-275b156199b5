import { NextRequest } from 'next/server';
import { TaskMasterService } from '@/lib/database/postgres-task-master-service';
import {
  withTaskMasterMiddleware,
  createApiResponse,
  createErrorResponse,
  ApiContext
} from '@/lib/task-master/api-middleware';
import { TaskSchemas, QuerySchemas } from '@/lib/task-master/schemas';

export const GET = withTaskMasterMiddleware(
  async (request: NextRequest, context: ApiContext) => {
    try {
      const { user, query } = context;

      // Query is already validated by middleware using QuerySchemas.taskListing
      // Query should be in camelCase (API layer standard)
      const projectId = query!.projectId;

      const taskMasterService = new TaskMasterService();

      // Check if user has access to this project
      const userProjects = await taskMasterService.getProjects(parseInt(user.id), false);
      const hasAccess = userProjects.some(p => p.id === projectId);

      if (!hasAccess) {
        return createApiResponse(undefined, {
          error: 'Access denied',
          status: 403,
          requestId: context.requestId
        });
      }

      // Get tasks for the project
      const tasks = await taskMasterService.getProjectTasks(projectId);

      return createApiResponse({ tasks });

    } catch (error) {
      console.error('Get tasks error:', error);
      return createErrorResponse(error, context.requestId);
    }
  },
  {
    requireAuth: true,
    sanitizeInput: true,
    validateQuery: QuerySchemas.taskListing
  }
);

export const POST = withTaskMasterMiddleware(
  async (request: NextRequest, context: ApiContext) => {
    console.log('🚀 Task creation started');
    console.log('📝 Request context:', {
      userId: context.user?.id,
      requestId: context.requestId,
      bodyKeys: Object.keys(context.body || {})
    });

    try {
      const { user, body } = context;

      // Body is already validated by middleware using TaskSchemas.createWithProject
      // Body should be in camelCase (API layer standard)
      const { projectId, ...taskData } = body;

      console.log('📊 Extracted data:', {
        projectId,
        taskDataKeys: Object.keys(taskData),
        userId: user.id,
        userIdType: typeof user.id
      });

      const taskMasterService = new TaskMasterService();
      console.log('✅ TaskMasterService instantiated');

      // Check if user has access to this project
      console.log('🔍 Checking user access to project:', projectId);
      const userProjects = await taskMasterService.getProjects(parseInt(user.id), false);
      console.log('📋 User projects retrieved:', userProjects.length, 'projects');

      const hasAccess = userProjects.some(p => p.id === projectId);
      console.log('🔐 Access check result:', hasAccess);

      if (!hasAccess) {
        console.log('❌ Access denied for user', user.id, 'to project', projectId);
        return createApiResponse(undefined, {
          error: 'Access denied',
          status: 403,
          requestId: context.requestId
        });
      }

      // Create task (service layer handles its own field mapping)
      // Add projectId to taskData and pass userId
      const taskDataWithProject = { ...taskData, project_id: projectId };
      console.log('📝 Task data prepared:', {
        ...taskDataWithProject,
        userId: parseInt(user.id)
      });

      console.log('🔨 Calling createTask...');
      const task = await taskMasterService.createTask(parseInt(user.id), taskDataWithProject);
      console.log('✅ Task created successfully:', task?.id);

      return createApiResponse({ task });

    } catch (error) {
      console.error('❌ Create task error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        name: error instanceof Error ? error.name : undefined,
        error: error
      });
      return createErrorResponse(error, context.requestId);
    }
  },
  {
    requireAuth: true,
    sanitizeInput: true,
    validateBody: TaskSchemas.createWithProject
  }
);
