import { NextRequest, NextResponse } from 'next/server';
import { SimpleTaskMasterService } from '@/lib/database/simple-task-master-service';

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing simplified seed data creation...');
    
    // Sample project for testing
    const testProject = {
      title: 'Test Project via Browser',
      description: 'Simple test project created via browser GET request',
      fullDescription: 'This is a test project created to verify the simplified TaskMaster service works correctly',
      eventLog: 'Test project created via browser',
      status: 'todo' as const,
      priority: 'medium' as const,
      visibility: 'public' as const
    };

    console.log('🔨 Creating SimpleTaskMasterService...');
    const taskMasterService = new SimpleTaskMasterService();
    console.log('✅ SimpleTaskMasterService created');

    // Use user ID 1 (should be the moderator)
    const userId = 1;
    console.log('👤 Using user ID:', userId);

    console.log('📝 Test project data:', JSON.stringify(testProject, null, 2));
    console.log('🔨 Calling createProject...');
    
    const project = await taskMasterService.createProject(userId, testProject);
    console.log('✅ Project created successfully:', project);

    // Also test getting projects
    console.log('📋 Testing getProjects...');
    const projects = await taskMasterService.getProjects(userId, false);
    console.log('✅ Projects retrieved:', projects.length, 'projects');

    return NextResponse.json({
      success: true,
      message: 'Simplified TaskMaster service test successful!',
      results: {
        projectCreated: {
          id: project.id,
          title: project.title,
          status: project.status
        },
        totalProjects: projects.length,
        allProjects: projects.map(p => ({
          id: p.id,
          title: p.title,
          status: p.status,
          createdAt: p.createdAt
        }))
      }
    });

  } catch (error) {
    console.error('❌ Simplified seed test failed:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      error: error
    });

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
