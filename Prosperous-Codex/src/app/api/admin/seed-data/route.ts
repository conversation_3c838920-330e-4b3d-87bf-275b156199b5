import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth/middleware';
import { TaskMasterService } from '@/lib/database/postgres-task-master-service';

// Sample data to create
const sampleProjects = [
  // To Do Tasks
  {
    title: 'Website Redesign',
    description: 'Update company website with new branding',
    full_description: 'Complete overhaul of the company website including new branding, improved user experience, mobile responsiveness, and modern design patterns. This project involves collaboration with the design team and requires extensive testing across multiple devices and browsers.',
    status: 'todo' as const,
    priority: 'high' as const,
    progress: 0,
    due_date: '2024-12-15',
    tags: ['design', 'frontend', 'branding']
  },
  {
    title: 'Database Migration',
    description: 'Migrate legacy database to new infrastructure',
    full_description: 'Plan and execute migration of legacy database systems to modern cloud infrastructure. Includes data validation, backup procedures, and minimal downtime deployment strategy.',
    status: 'todo' as const,
    priority: 'high' as const,
    progress: 15,
    due_date: '2024-12-20',
    tags: ['backend', 'database', 'infrastructure']
  },
  {
    title: 'User Authentication System',
    description: 'Implement secure user authentication',
    full_description: 'Design and implement a comprehensive user authentication system with multi-factor authentication, password policies, and session management.',
    status: 'todo' as const,
    priority: 'medium' as const,
    progress: 25,
    due_date: '2025-01-10',
    tags: ['security', 'backend', 'authentication']
  },

  // In Progress Tasks
  {
    title: 'Mobile App Development',
    description: 'Build iOS and Android applications',
    full_description: 'Create native mobile applications for both iOS and Android platforms with cross-platform functionality. Features include user authentication, real-time notifications, and offline capabilities.',
    status: 'inProgress' as const,
    priority: 'medium' as const,
    progress: 40,
    due_date: '2025-01-30',
    tags: ['mobile', 'ios', 'android', 'react-native']
  },
  {
    title: 'API Documentation',
    description: 'Create comprehensive API documentation',
    full_description: 'Develop detailed API documentation including endpoint descriptions, request/response examples, authentication methods, and integration guides for third-party developers.',
    status: 'inProgress' as const,
    priority: 'medium' as const,
    progress: 60,
    due_date: '2024-12-30',
    tags: ['documentation', 'api', 'developer-tools']
  },
  {
    title: 'Performance Optimization',
    description: 'Optimize application performance',
    full_description: 'Analyze and improve application performance including database query optimization, caching strategies, and frontend bundle size reduction.',
    status: 'inProgress' as const,
    priority: 'low' as const,
    progress: 75,
    due_date: '2025-02-15',
    tags: ['performance', 'optimization', 'caching']
  },

  // Completed Tasks
  {
    title: 'Brand Guidelines',
    description: 'Company brand identity documentation',
    full_description: 'Comprehensive brand guidelines including logo usage, typography, color palette, and brand voice. This document serves as the foundation for all company communications and design work.',
    status: 'completed' as const,
    priority: 'high' as const,
    progress: 100,
    due_date: '2024-11-30',
    completed_date: '2024-11-28',
    tags: ['branding', 'design', 'documentation']
  },
  {
    title: 'Security Audit',
    description: 'Comprehensive security assessment',
    full_description: 'Complete security audit of all systems including penetration testing, vulnerability assessment, and compliance review. All critical issues have been addressed.',
    status: 'completed' as const,
    priority: 'high' as const,
    progress: 100,
    due_date: '2024-11-15',
    completed_date: '2024-11-10',
    tags: ['security', 'audit', 'compliance']
  },
  {
    title: 'Email Templates',
    description: 'Design and implement email templates',
    full_description: 'Responsive email templates for notifications, newsletters, and transactional emails. All templates are tested across major email clients and include dark mode support.',
    status: 'completed' as const,
    priority: 'low' as const,
    progress: 100,
    due_date: '2024-11-20',
    completed_date: '2024-11-15',
    tags: ['email', 'templates', 'design']
  }
];

export async function POST(request: NextRequest) {
  try {
    // Authenticate user and check admin access
    const authResult = await requireAuth(request);
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { user } = authResult;
    
    // Check if user is admin or moderator
    if (user.role !== 'admin' && user.role !== 'moderator') {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    console.log('🌱 Starting admin seed data creation...');
    const taskMasterService = new TaskMasterService();
    console.log('✅ TaskMasterService created');

    // Get current user ID for creating projects
    const userId = parseInt(user.id);
    console.log('👤 User ID:', userId, 'Role:', user.role);

    // Create sample projects
    const createdProjects = [];
    console.log('📝 Creating', sampleProjects.length, 'sample projects...');

    for (let i = 0; i < sampleProjects.length; i++) {
      const projectData = sampleProjects[i];
      try {
        console.log(`📋 Creating project ${i + 1}/${sampleProjects.length}: ${projectData.title}`);
        const project = await taskMasterService.createProject(userId, projectData);
        createdProjects.push(project);
        console.log(`✅ Created project: ${project.title} (ID: ${project.id})`);

        // Skip comments for now to avoid potential issues
        console.log(`⏭️ Skipping comments for debugging`);
      } catch (error) {
        console.error(`❌ Failed to create project: ${projectData.title}`, {
          message: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
          error: error
        });
      }
    }

    console.log(`🎉 Completed: Created ${createdProjects.length} out of ${sampleProjects.length} projects`);
    
    return NextResponse.json({
      success: true,
      message: `Successfully created ${createdProjects.length} sample projects for Task Master`,
      projects: createdProjects.length,
      attempted: sampleProjects.length,
      details: createdProjects.map(p => ({ id: p.id, title: p.title, status: p.status }))
    });

  } catch (error) {
    console.error('❌ Admin seed data error:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      error: error
    });
    return NextResponse.json({
      success: false,
      error: 'Failed to create sample data',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Authenticate user and check admin access
    const authResult = await requireAuth(request);
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }
    
    const { user } = authResult;
    
    // Check if user is admin or moderator
    if (user.role !== 'admin' && user.role !== 'moderator') {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const taskMasterService = new TaskMasterService();
    
    // Get all projects for the user
    const projects = taskMasterService.getUserProjects(parseInt(user.id));
    
    // Delete all projects
    let deletedCount = 0;
    for (const project of projects) {
      try {
        await taskMasterService.deleteProject(project.id, user);
        deletedCount++;
      } catch (error) {
        console.warn(`Failed to delete project ${project.id}:`, error);
      }
    }
    
    return NextResponse.json({
      message: `Successfully deleted ${deletedCount} projects`,
      deletedCount
    });
    
  } catch (error) {
    console.error('Clear data error:', error);
    return NextResponse.json(
      { error: 'Failed to clear sample data' },
      { status: 500 }
    );
  }
}
