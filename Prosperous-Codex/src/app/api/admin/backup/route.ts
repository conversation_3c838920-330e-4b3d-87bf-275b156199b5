import { NextRequest, NextResponse } from 'next/server';
import { requireRole } from '@/lib/auth/middleware';

// Backup functionality temporarily disabled during PostgreSQL migration
// TODO: Implement PostgreSQL-compatible backup system

export async function GET(request: NextRequest) {
  try {
    // Require admin role
    const authResult = await requireRole(request, 'admin');
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    return NextResponse.json({
      error: 'Backup functionality is temporarily disabled during PostgreSQL migration',
      message: 'Please use Vercel Postgres backup features or contact administrator'
    }, { status: 503 });

  } catch (error) {
    console.error('Backup error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Require admin role
    const authResult = await requireRole(request, 'admin');
    
    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    return NextResponse.json({
      error: 'Backup functionality is temporarily disabled during PostgreSQL migration',
      message: 'Please use Vercel Postgres backup features or contact administrator'
    }, { status: 503 });

  } catch (error) {
    console.error('Backup error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
