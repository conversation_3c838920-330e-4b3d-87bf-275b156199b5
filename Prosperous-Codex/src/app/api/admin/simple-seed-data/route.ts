import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth/middleware';
import { SimpleTaskMasterService } from '@/lib/database/simple-task-master-service';

// Sample projects data in camelCase format
const sampleProjects = [
  {
    title: 'Website Redesign Project',
    description: 'Complete overhaul of company website with modern design and improved UX',
    fullDescription: 'This project involves redesigning our entire corporate website to improve user experience, modernize the visual design, and optimize for mobile devices. The project will include user research, wireframing, design mockups, development, and testing phases.',
    eventLog: 'Project initiated based on user feedback and analytics showing high bounce rates on current site.',
    status: 'inProgress' as const,
    priority: 'high' as const,
    visibility: 'public' as const
  },
  {
    title: 'Mobile App Development',
    description: 'Develop native mobile application for iOS and Android platforms',
    fullDescription: 'Create a comprehensive mobile application that provides core functionality to our users on-the-go. The app will feature user authentication, real-time data synchronization, push notifications, and offline capabilities.',
    eventLog: 'Market research completed. Technical requirements defined. Development team assembled.',
    status: 'todo' as const,
    priority: 'high' as const,
    visibility: 'public' as const
  },
  {
    title: 'Database Migration',
    description: 'Migrate legacy database to modern PostgreSQL infrastructure',
    fullDescription: 'Comprehensive migration of our legacy database system to PostgreSQL, including data transformation, performance optimization, and implementation of modern backup and recovery procedures.',
    eventLog: 'Legacy system analysis completed. Migration strategy approved by technical committee.',
    status: 'todo' as const,
    priority: 'medium' as const,
    visibility: 'public' as const
  },
  {
    title: 'Security Audit',
    description: 'Comprehensive security assessment and vulnerability remediation',
    fullDescription: 'Full security audit of all systems and applications, including penetration testing, code review, and implementation of security best practices. Will result in detailed security recommendations and remediation plan.',
    eventLog: 'Security audit scheduled. External security firm contracted. Initial scoping meeting completed.',
    status: 'completed' as const,
    priority: 'high' as const,
    visibility: 'public' as const
  },
  {
    title: 'API Documentation Update',
    description: 'Update and improve API documentation for developers',
    fullDescription: 'Comprehensive update of API documentation including interactive examples, better organization, and improved developer onboarding materials. Will include automated documentation generation from code.',
    eventLog: 'Current documentation reviewed. Developer feedback collected. New documentation structure planned.',
    status: 'inProgress' as const,
    priority: 'low' as const,
    visibility: 'public' as const
  }
];

export async function GET(request: NextRequest) {
  // Allow GET requests for easy browser testing
  return POST(request);
}

export async function POST(request: NextRequest) {
  try {
    console.log('🌱 Starting simplified admin seed data creation...');
    
    // Check authentication
    const authResult = await requireAuth(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const user = authResult.user;
    console.log('👤 Authenticated user:', user.id, user.role);

    // Check admin permissions
    if (user.role !== 'admin' && user.role !== 'moderator') {
      return NextResponse.json(
        { error: 'Admin permissions required' },
        { status: 403 }
      );
    }

    console.log('🔨 Creating SimpleTaskMasterService...');
    const taskMasterService = new SimpleTaskMasterService();
    console.log('✅ SimpleTaskMasterService created');

    const userId = parseInt(user.id);
    console.log('👤 Using user ID:', userId);

    const createdProjects = [];
    console.log('📝 Creating', sampleProjects.length, 'sample projects...');

    for (let i = 0; i < sampleProjects.length; i++) {
      const projectData = sampleProjects[i];
      try {
        console.log(`📋 Creating project ${i + 1}/${sampleProjects.length}: ${projectData.title}`);
        console.log('📝 Project data:', JSON.stringify(projectData, null, 2));
        
        const project = await taskMasterService.createProject(userId, projectData);
        createdProjects.push(project);
        console.log(`✅ Created project: ${project.title} (ID: ${project.id})`);

      } catch (error) {
        console.error(`❌ Failed to create project: ${projectData.title}`, {
          message: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
          error: error
        });
      }
    }

    console.log(`🎉 Completed: Created ${createdProjects.length} out of ${sampleProjects.length} projects`);

    return NextResponse.json({
      success: true,
      message: `Successfully created ${createdProjects.length} sample projects for Task Master (Simplified)`,
      projects: createdProjects.length,
      attempted: sampleProjects.length,
      details: createdProjects.map(p => ({ 
        id: p.id, 
        title: p.title, 
        status: p.status 
      }))
    });

  } catch (error) {
    console.error('❌ Simplified admin seed data error:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      error: error
    });
    
    return NextResponse.json({
      success: false,
      error: 'Failed to create sample data (simplified)',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
