import { NextRequest, NextResponse } from 'next/server';
import { initializeApp, getDatabaseStatus, resetDatabase } from '@/lib/database/init';

export async function GET() {
  try {
    const status = getDatabaseStatus();
    return NextResponse.json(status);
  } catch {
    return NextResponse.json(
      { error: 'Failed to get database status' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();

    if (action === 'initialize') {
      // Check if database is already initialized (has users)
      const status = getDatabaseStatus();
      if (status.stats.users > 0) {
        // If users exist, require authentication for re-initialization
        const { requireAuth } = await import('@/lib/auth/middleware');
        const authResult = await requireAuth(request);

        if ('error' in authResult) {
          return NextResponse.json(
            { error: 'Database already initialized. Authentication required for re-initialization.' },
            { status: 401 }
          );
        }
      }

      const result = await initializeApp();

      if (result.success) {
        return NextResponse.json({
          message: 'Database initialized successfully',
          status: getDatabaseStatus()
        });
      } else {
        return NextResponse.json(
          { error: 'Database initialization failed', details: result.error },
          { status: 500 }
        );
      }
    } else if (action === 'reset') {
      if (process.env.NODE_ENV === 'production') {
        return NextResponse.json(
          { error: 'Database reset is not allowed in production' },
          { status: 403 }
        );
      }

      // Reset always requires authentication
      const { requireAuth } = await import('@/lib/auth/middleware');
      const authResult = await requireAuth(request);

      if ('error' in authResult) {
        return NextResponse.json(
          { error: authResult.error },
          { status: authResult.status }
        );
      }

      const result = await resetDatabase();

      if (result.success) {
        return NextResponse.json({
          message: 'Database reset successfully',
          backupName: result.backupName,
          status: getDatabaseStatus()
        });
      } else {
        return NextResponse.json(
          { error: 'Database reset failed', details: result.error },
          { status: 500 }
        );
      }
    } else {
      return NextResponse.json(
        { error: 'Invalid action. Use "initialize" or "reset"' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Database operation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
