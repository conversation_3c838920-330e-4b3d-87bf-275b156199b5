import { NextRequest, NextResponse } from 'next/server';
import { TaskMasterService } from '@/lib/database/task-master-service';
import { requireAuth } from '@/lib/auth/middleware';

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request);

    if ('error' in authResult) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      );
    }

    const { user } = authResult;
    const taskMasterService = new TaskMasterService();

    // Get all user projects
    const allProjects = taskMasterService.getUserProjects(parseInt(user.id));

    // Calculate active projects (not completed)
    const activeProjects = allProjects.filter(project => project.status !== 'completed');
    const activeProjectsCount = activeProjects.length;

    // Calculate new projects (created within last 24 hours)
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const newProjects = activeProjects.filter(project => {
      const createdAt = new Date(project.createdAt);
      return createdAt >= twentyFourHoursAgo;
    });
    const newProjectsCount = newProjects.length;

    // Calculate project progress for dashboard display
    const projectProgress = activeProjects
      .map(project => {
        // Calculate progress from task summary
        let progress = 0;
        if (project.taskSummary && project.taskSummary.total > 0) {
          progress = Math.round((project.taskSummary.completed / project.taskSummary.total) * 100);
        }

        return {
          name: project.title,
          progress: progress
        };
      })
      .sort((a, b) => b.progress - a.progress) // Sort by progress descending
      .slice(0, 6); // Limit to 6 projects for UI space

    // Get enhanced recent activity with current titles and deduplication
    const recentActivity = taskMasterService.getDashboardActivity(parseInt(user.id));

    return NextResponse.json({
      activeProjects: {
        count: activeProjectsCount,
        newCount: newProjectsCount
      },
      recentActivity,
      projectProgress
    });

  } catch (error) {
    console.error('Dashboard stats error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
