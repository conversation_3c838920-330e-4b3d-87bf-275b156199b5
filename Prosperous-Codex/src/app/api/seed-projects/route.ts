import { NextRequest, NextResponse } from 'next/server';
import { TaskMasterService } from '@/lib/database/postgres-task-master-service';

export async function POST(request: NextRequest) {
  try {
    console.log('🌱 Seeding projects...');
    
    const taskMasterService = new TaskMasterService();
    
    // Create sample projects for user ID 1 (moderator)
    const sampleProjects = [
      {
        title: 'Sample Project 1',
        description: 'A sample project for testing Task Master functionality',
        full_description: 'This is a comprehensive sample project created to test the Task Master system. It includes various features and capabilities.',
        event_log: 'Project created for testing purposes',
        status: 'todo' as const,
        priority: 'medium' as const,
        visibility: 'public' as const
      },
      {
        title: 'Demo Project',
        description: 'Demo project for showcasing features',
        full_description: 'This demo project showcases the various features available in the Task Master system.',
        event_log: 'Demo project initialized',
        status: 'inProgress' as const,
        priority: 'high' as const,
        visibility: 'public' as const
      },
      {
        title: 'Test Project',
        description: 'Project for testing task creation',
        full_description: 'This project is specifically designed for testing task creation and management features.',
        event_log: 'Test project setup complete',
        status: 'todo' as const,
        priority: 'low' as const,
        visibility: 'public' as const
      }
    ];
    
    const createdProjects = [];
    
    for (const projectData of sampleProjects) {
      console.log(`📝 Creating project: ${projectData.title}`);
      try {
        const project = await taskMasterService.createProject(1, projectData);
        createdProjects.push(project);
        console.log(`✅ Created project: ${project.title} (ID: ${project.id})`);
      } catch (error) {
        console.error(`❌ Failed to create project ${projectData.title}:`, error);
      }
    }
    
    return NextResponse.json({
      success: true,
      message: `Successfully created ${createdProjects.length} projects`,
      projects: createdProjects.map(p => ({
        id: p.id,
        title: p.title,
        status: p.status
      }))
    });
    
  } catch (error) {
    console.error('❌ Seed projects failed:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : undefined,
      error: error
    });
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
