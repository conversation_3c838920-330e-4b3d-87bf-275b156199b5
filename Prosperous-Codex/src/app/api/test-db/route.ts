import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database/database';

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing database connection...');
    
    // Test basic database connection
    const pool = getDatabase();
    console.log('✅ Database pool created');
    
    const client = await pool.connect();
    console.log('✅ Database client connected');
    
    try {
      // Test basic query
      const result = await client.query('SELECT 1 as test');
      console.log('✅ Basic query successful:', result.rows);
      
      // Test users table
      const usersResult = await client.query('SELECT COUNT(*) as count FROM users');
      console.log('✅ Users table query successful:', usersResult.rows);
      
      // Test projects table
      const projectsResult = await client.query('SELECT COUNT(*) as count FROM projects');
      console.log('✅ Projects table query successful:', projectsResult.rows);
      
      return NextResponse.json({
        success: true,
        message: 'Database connection successful',
        tests: {
          basicQuery: result.rows[0],
          userCount: usersResult.rows[0],
          projectCount: projectsResult.rows[0]
        }
      });
      
    } finally {
      client.release();
      console.log('✅ Database client released');
    }
    
  } catch (error) {
    console.error('❌ Database test failed:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : undefined,
      error: error
    });
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
