"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useTranslations } from 'next-intl';
import { Plus, UserPlus, Calendar } from 'lucide-react';
import { ActivityLogEntry } from '@/lib/task-master/types';

// Dashboard-specific interface
interface DashboardStats {
  activeProjects: {
    count: number;
    newCount: number;
  };
  recentActivity: ActivityLogEntry[];
  projectProgress: Array<{
    name: string;
    progress: number;
  }>;
}

export function QuickActions() {
  const t = useTranslations('dashboard.actions');
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardStats = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/dashboard/stats');
        if (!response.ok) {
          throw new Error('Failed to fetch dashboard stats');
        }

        const data = await response.json();
        setDashboardStats(data);
      } catch (err) {
        console.error('Error fetching dashboard stats:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardStats();
  }, []);

  const projects = dashboardStats?.projectProgress || [];

  return (
    <div className="flex w-full flex-col items-start gap-4">
      <span className="text-base font-semibold text-foreground">
        {t('title')}
      </span>
      <div className="flex w-full flex-col items-start gap-2">
        <Button className="h-8 w-full flex-none" variant="brand-primary">
          <Plus className="h-4 w-4" />
          {t('createProject')}
        </Button>
        <Button className="h-8 w-full flex-none" variant="neutral-secondary">
          <UserPlus className="h-4 w-4" />
          {t('inviteTeam')}
        </Button>
        <Button className="h-8 w-full flex-none" variant="neutral-secondary">
          <Calendar className="h-4 w-4" />
          {t('scheduleMeeting')}
        </Button>
      </div>
      <div className="flex w-full flex-col items-start gap-4 rounded-md border border-border bg-card/50 backdrop-blur-sm px-4 py-4">
        <span className="text-sm font-medium text-foreground">
          Project Progress
        </span>
        {loading ? (
          <div className="flex w-full items-center justify-center py-4">
            <span className="text-sm text-muted-foreground">Loading...</span>
          </div>
        ) : error ? (
          <div className="flex w-full items-center justify-center py-4">
            <span className="text-sm text-muted-foreground">Unable to load projects</span>
          </div>
        ) : projects.length === 0 ? (
          <div className="flex w-full items-center justify-center py-4">
            <span className="text-sm text-muted-foreground">No active projects</span>
          </div>
        ) : (
          projects.map((project, index) => (
            <div key={index} className="flex w-full items-center gap-3">
              <span className="flex-1 text-sm text-foreground min-w-0 truncate">
                {project.name}
              </span>
              <div className="flex flex-1 items-center gap-2">
                <Progress
                  value={project.progress}
                  variant="brand"
                  className="flex-1"
                  showPercentage={false}
                />
                <span className="text-sm font-medium text-[#5E6AD2] dark:text-[#9E8CFC] min-w-[3rem] text-right">
                  {project.progress}%
                </span>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
