import { initializeDatabase, getDatabaseStats, healthCheck } from './database';
import { UserService } from './user-service';
import { unlinkSync, existsSync } from 'fs';
import { join } from 'path';

/**
 * Initialize the database and create default data
 */
export async function initializeApp() {
  try {
    console.log('Initializing Prosperous Codex database...');
    
    // Initialize database and create tables
    initializeDatabase();
    
    // Initialize user service
    const userService = new UserService();
    
    // Create default admin user if it doesn't exist
    const existingAdmin = await userService.getUserByEmail("<EMAIL>");

    if (!existingAdmin) {
      await userService.createUser({
        email: "<EMAIL>",
        username: "admin",
        password: "password", // TODO: Change this in production!
        role: "admin"
      });
      console.log('✅ Default admin user created (<EMAIL> / password)');
    } else {
      console.log('✅ Admin user already exists');
    }

    // Create default moderator user if it doesn't exist
    const existingModerator = await userService.getUserByEmail("<EMAIL>");

    if (!existingModerator) {
      await userService.createUser({
        email: "<EMAIL>",
        username: "moderator",
        password: "moderator123", // TODO: Change this in production!
        role: "moderator"
      });
      console.log('✅ Default moderator user created (<EMAIL> / moderator123)');
    } else {
      console.log('✅ Moderator user already exists');
    }
    
    console.log('✅ Database initialization completed successfully');
    
    return { success: true };
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    return { success: false, error };
  }
}

/**
 * Reset database (for development only)
 */
export async function resetDatabase() {
  if (process.env.NODE_ENV === 'production') {
    throw new Error('Database reset is not allowed in production');
  }

  try {
    const dbPath = process.env.DATABASE_PATH || join(process.cwd(), 'data', 'prosperous-codex.db');

    let backupName = null;

    // Create backup before reset if database exists
    if (existsSync(dbPath)) {
      console.log('📦 Creating backup before reset...');

      // Import backup function
      const { createDatabaseBackup } = await import('../../app/api/admin/backup/route');
      const backupResult = await createDatabaseBackup('auto-reset');

      if (backupResult.success) {
        backupName = backupResult.backupName;
        console.log(`✅ Backup created: ${backupName}`);
      } else {
        console.warn('⚠️ Backup failed, proceeding with reset anyway');
      }

      // CRITICAL: Close existing database connection before deleting file
      console.log('🔄 Closing existing database connection...');
      const { forceResetDatabase } = await import('./database');
      forceResetDatabase();

      // Delete database file
      unlinkSync(dbPath);
      console.log('🗑️ Database file deleted');
    }

    // Reinitialize database
    await initializeApp();

    // Refresh any cached service connections
    console.log('🔄 Refreshing service connections...');
    try {
      // Clear any authentication caches
      const { userVerificationCache } = await import('../auth/middleware');
      if (userVerificationCache && typeof userVerificationCache.clear === 'function') {
        userVerificationCache.clear();
      }
    } catch (error) {
      console.warn('⚠️ Failed to clear auth cache:', error);
    }

    console.log('✅ Database reset completed');
    return { success: true, backupName };
  } catch (error) {
    console.error('❌ Database reset failed:', error);
    return { success: false, error };
  }
}

/**
 * Get database status and statistics
 */
export function getDatabaseStatus() {
  try {
    const stats = getDatabaseStats();
    const healthy = healthCheck();
    
    return {
      healthy,
      stats,
      initialized: true
    };
  } catch (error) {
    return {
      healthy: false,
      stats: null,
      initialized: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
