import { getDatabase, executeTransaction } from './database';
import { Pool, PoolClient } from 'pg';
// User import removed as it's not used directly in this file
import { FieldMapper } from '../task-master/field-mapping';
import {
  ErrorFactory,
  isTaskMasterError
} from '../task-master/errors';
import {
  Project,
  Task,
  ProjectTeamMember,
  ProjectFile,
  ProjectComment,
  ActivityLogEntry,
  ProjectEditHistory,
  CreateProjectInput,
  CreateTaskInput,
  ProjectDbRow,
  TaskDbRow,
  ProjectTeamMemberDbRow,
  ProjectCommentDbRow,
  ActivityLogEntryDbRow,
  ProjectEditHistoryDbRow,
  DataMapper
} from '../task-master/types';
import { AuthorizationService } from '../task-master/authorization';
import { sanitize } from '../task-master/sanitization';

// All interfaces are now imported from ../task-master/types.ts
// This removes duplication and ensures consistency

export class TaskMasterService {
  private pool = getDatabase();
  private authService = new AuthorizationService();

  /**
   * Create a new project
   */
  async createProject(userId: number, projectData: CreateProjectInput): Promise<Project> {
    const {
      title,
      description,
      full_description: fullDescription, // Handle snake_case from API middleware
      event_log: eventLog, // Handle snake_case from API middleware
      status = 'todo',
      priority = 'medium',
      progress = 0,
      due_date: dueDate, // Handle snake_case from API middleware
      assigned_to: assignedTo, // Handle snake_case from API middleware
      visibility = 'public',
      tags = []
    } = projectData;

    // Validate and sanitize inputs
    const sanitizedTitle = sanitize(title, 'title');
    const sanitizedDescription = sanitize(description || '', 'description');
    const sanitizedFullDescription = sanitize(fullDescription || '', 'full_description');
    const sanitizedEventLog = sanitize(eventLog || '', 'event_log');
    const sanitizedTags = tags.map(tag => sanitize(tag, 'tag')).filter(tag => tag.length > 0);

    // Validate status
    if (!['todo', 'inProgress', 'completed'].includes(status)) {
      throw ErrorFactory.invalidInput('status', status, 'Status must be todo, inProgress, or completed');
    }

    // Validate priority
    if (!['low', 'medium', 'high'].includes(priority)) {
      throw ErrorFactory.invalidInput('priority', priority, 'Priority must be low, medium, or high');
    }

    try {
      // Use database transaction for atomic operation
      const result = await executeTransaction(async (client: PoolClient) => {
        // Insert project
        const projectResult = await client.query(`
          INSERT INTO projects (title, description, full_description, event_log, status, priority, progress, due_date, visibility, created_by, assigned_to)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
          RETURNING id
        `, [
          sanitizedTitle,
          sanitizedDescription,
          sanitizedFullDescription,
          sanitizedEventLog,
          status,
          priority,
          progress,
          dueDate || null,
          visibility,
          userId,
          assignedTo || null
        ]);

        const projectId = projectResult.rows[0].id;

        // Add tags if provided
        if (sanitizedTags.length > 0) {
          for (const tag of sanitizedTags) {
            await client.query(`
              INSERT INTO project_tags (project_id, tag_name)
              VALUES ($1, $2)
            `, [projectId, tag]);
          }
        }

        // Add project creator as team member with 'owner' role
        await client.query(`
          INSERT INTO project_team_members (project_id, user_id, role, added_by)
          VALUES ($1, $2, $3, $4)
        `, [projectId, userId, 'owner', userId]);

        // Log activity within transaction
        await client.query(`
          INSERT INTO activity_log (project_id, user_id, activity_type, description, created_at)
          VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
        `, [projectId, userId, 'creation', `Project "${sanitizedTitle}" created`]);

        return projectId;
      });

      // Return the created project
      return await this.getProjectById(result);
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }

      console.error('Error creating project:', error);
      throw ErrorFactory.databaseOperation('create', `Failed to create project: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Update a project
   */
  async updateProject(projectId: number, userId: number, updateData: Partial<CreateProjectInput>): Promise<Project> {
    // Validate input
    if (!projectId || projectId <= 0) {
      throw ErrorFactory.invalidInput('projectId', projectId, 'Project ID must be a positive number');
    }
    if (!userId || userId <= 0) {
      throw ErrorFactory.invalidInput('userId', userId, 'User ID must be a positive number');
    }

    try {
      const client = await this.pool.connect();
      try {
        // Get current project to check permissions
        const currentProject = await this.getProjectById(projectId);
        if (!currentProject) {
          throw ErrorFactory.notFound('Project', projectId);
        }

        // Check permissions
        if (!(await this.authService.canModifyProject(projectId, userId))) {
          throw ErrorFactory.unauthorized('update project', 'Insufficient permissions to update this project');
        }

        // Build update query dynamically
        const updateFields = [];
        const values = [];
        let paramIndex = 1;

        if (updateData.title !== undefined) {
          updateFields.push(`title = $${paramIndex++}`);
          values.push(sanitize(updateData.title, 'title'));
        }
        if (updateData.description !== undefined) {
          updateFields.push(`description = $${paramIndex++}`);
          values.push(sanitize(updateData.description || '', 'description'));
        }
        if (updateData.full_description !== undefined) {
          updateFields.push(`full_description = $${paramIndex++}`);
          values.push(sanitize(updateData.full_description || '', 'full_description'));
        }
        if (updateData.event_log !== undefined) {
          updateFields.push(`event_log = $${paramIndex++}`);
          values.push(sanitize(updateData.event_log || '', 'event_log'));
        }
        if (updateData.status !== undefined) {
          updateFields.push(`status = $${paramIndex++}`);
          values.push(updateData.status);
        }
        if (updateData.priority !== undefined) {
          updateFields.push(`priority = $${paramIndex++}`);
          values.push(updateData.priority);
        }
        if (updateData.progress !== undefined) {
          updateFields.push(`progress = $${paramIndex++}`);
          values.push(updateData.progress);
        }
        if (updateData.due_date !== undefined) {
          updateFields.push(`due_date = $${paramIndex++}`);
          values.push(updateData.due_date);
        }
        if (updateData.assigned_to !== undefined) {
          updateFields.push(`assigned_to = $${paramIndex++}`);
          values.push(updateData.assigned_to);
        }
        if (updateData.visibility !== undefined) {
          updateFields.push(`visibility = $${paramIndex++}`);
          values.push(updateData.visibility);
        }

        if (updateFields.length === 0) {
          return currentProject; // No changes
        }

        updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
        values.push(projectId);

        const query = `
          UPDATE projects
          SET ${updateFields.join(', ')}
          WHERE id = $${paramIndex}
        `;

        await client.query(query, values);

        // Log activity
        await client.query(`
          INSERT INTO activity_log (project_id, user_id, activity_type, description)
          VALUES ($1, $2, $3, $4)
        `, [projectId, userId, 'project_update', `Project "${currentProject.title}" updated`]);

        return await this.getProjectById(projectId);
      } finally {
        client.release();
      }
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }
      console.error('Error updating project:', error);
      throw ErrorFactory.databaseOperation('update', `Failed to update project: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get project by ID with optional includes
   */
  async getProjectById(projectId: number, options: {
    includeTasks?: boolean;
    includeActivity?: boolean;
    includeFiles?: boolean;
    includeComments?: boolean;
    includeTeamMembers?: boolean;
  } = {}): Promise<Project> {
    const {
      includeTasks = false,
      includeActivity = false,
      includeFiles = false,
      includeComments = false,
      includeTeamMembers = false
    } = options;

    // Validate input
    if (!projectId || projectId <= 0) {
      throw ErrorFactory.invalidInput('projectId', projectId, 'Project ID must be a positive number');
    }

    try {
      const client = await this.pool.connect();
      try {
        // Get basic project data
        const projectResult = await client.query(`
          SELECT p.*, u1.username as created_by_username, u2.username as assigned_to_username
          FROM projects p
          LEFT JOIN users u1 ON p.created_by = u1.id
          LEFT JOIN users u2 ON p.assigned_to = u2.id
          WHERE p.id = $1
        `, [projectId]);

        if (projectResult.rows.length === 0) {
          throw ErrorFactory.notFound('Project', projectId);
        }

        const projectRow = projectResult.rows[0];
        const result = DataMapper.projectFromDb(projectRow);

        // Get tags (always included as they're lightweight)
        const tagsResult = await client.query(`
          SELECT tag_name FROM project_tags WHERE project_id = $1
        `, [projectId]);
        result.tags = tagsResult.rows.map(row => row.tag_name);

        // Conditionally load additional data
        if (includeTeamMembers) {
          const teamResult = await client.query(`
            SELECT ptm.*, u.username, u.email
            FROM project_team_members ptm
            JOIN users u ON ptm.user_id = u.id
            WHERE ptm.project_id = $1
          `, [projectId]);
          result.teamMembers = DataMapper.teamMembersFromDb(teamResult.rows);
        }

        if (includeFiles) {
          result.files = await this.getProjectFiles(projectId);
        }

        if (includeComments) {
          result.comments = await this.getProjectComments(projectId);
        }

        if (includeTasks) {
          result.tasks = await this.getProjectTasks(projectId);
        }

        if (includeActivity) {
          result.activity = await this.getProjectActivity(projectId);
        }

        return result;
      } finally {
        client.release();
      }
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }

      console.error('Error getting project by ID:', error);
      throw ErrorFactory.databaseOperation('retrieve', `Failed to retrieve project ${projectId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get project by ID with full details (backward compatibility)
   */
  async getProjectByIdFull(projectId: number): Promise<Project> {
    return this.getProjectById(projectId, {
      includeTasks: true,
      includeActivity: true,
      includeFiles: true,
      includeComments: true,
      includeTeamMembers: true
    });
  }

  /**
   * Get all projects for a user with optional details
   */
  async getProjects(userId: number, includeDetails: boolean = false): Promise<Project[]> {
    console.log('🔍 getProjects called with:', { userId, includeDetails, userIdType: typeof userId });

    // Validate input
    if (!userId || isNaN(userId) || userId <= 0) {
      console.error('❌ Invalid userId:', userId);
      throw ErrorFactory.invalidInput('userId', userId, 'User ID must be a positive number');
    }

    try {
      console.log('🔌 Connecting to database...');
      const client = await this.pool.connect();
      console.log('✅ Database connected');
      try {
        // Get projects where user is creator, assigned, or team member
        console.log('📊 Executing projects query for userId:', userId);
        const projectsResult = await client.query(`
          SELECT DISTINCT p.*, u1.username as created_by_username, u2.username as assigned_to_username
          FROM projects p
          LEFT JOIN users u1 ON p.created_by = u1.id
          LEFT JOIN users u2 ON p.assigned_to = u2.id
          LEFT JOIN project_team_members ptm ON p.id = ptm.project_id
          WHERE p.created_by = $1
             OR p.assigned_to = $1
             OR ptm.user_id = $1
             OR p.visibility = 'public'
          ORDER BY p.updated_at DESC
        `, [userId]);
        console.log('📋 Query result:', projectsResult.rows.length, 'rows');

        // Convert to API format and optionally include details
        console.log('🔄 Converting projects to API format...');
        const projects = await Promise.all(projectsResult.rows.map(async (projectRow, index) => {
          try {
            console.log(`📝 Processing project ${index + 1}:`, projectRow.id, projectRow.title);
            const project = DataMapper.projectFromDb(projectRow);
            console.log(`✅ Project ${index + 1} converted successfully`);

            // Always include tags (lightweight)
            const tagsResult = await client.query(`
              SELECT tag_name FROM project_tags WHERE project_id = $1
            `, [project.id]);
            project.tags = tagsResult.rows.map(row => row.tag_name);

            // Always include task count for frontend functionality
            const taskCountResult = await client.query(`
              SELECT
                COUNT(*) as total,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed
              FROM tasks WHERE project_id = $1
            `, [project.id]);
            const taskSummary = taskCountResult.rows[0];

            const result = {
              ...project,
              taskCount: parseInt(taskSummary.total) || 0,
              completedTaskCount: parseInt(taskSummary.completed) || 0,
              progress: parseInt(taskSummary.total) > 0 
                ? Math.round((parseInt(taskSummary.completed) / parseInt(taskSummary.total)) * 100)
                : 0
            };

            // Only load heavy data if explicitly requested
            if (includeDetails) {
              // Get team members
              const teamResult = await client.query(`
                SELECT ptm.*, u.username, u.email
                FROM project_team_members ptm
                JOIN users u ON ptm.user_id = u.id
                WHERE ptm.project_id = $1
              `, [project.id]);
              result.teamMembers = teamResult.rows.map(DataMapper.teamMemberFromDb);

              // Get recent files (limited)
              result.files = await this.getProjectFiles(project.id, { limit: 5 });

              // Get recent comments (limited)
              result.comments = await this.getProjectComments(project.id, { limit: 5 });
            }

            return result;
          } catch (error) {
            console.error(`Error processing project ${projectRow.id}:`, error);
            // Return basic project data if detailed processing fails
            return DataMapper.projectFromDb(projectRow);
          }
        }));

        console.log('✅ All projects processed successfully, returning:', projects.length, 'projects');
        return projects;
      } finally {
        console.log('🔌 Releasing database connection');
        client.release();
      }
    } catch (error) {
      if (isTaskMasterError(error)) {
        console.error('❌ TaskMaster error in getProjects:', error);
        throw error;
      }

      console.error('❌ Database error in getProjects:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        name: error instanceof Error ? error.name : undefined,
        error: error
      });
      throw ErrorFactory.databaseOperation('retrieve', `Failed to retrieve projects: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get user projects with pagination and filtering (API compatibility method)
   */
  getUserProjects(userId: number, options?: {
    includeDetails?: boolean;
    limit?: number;
    offset?: number;
    status?: string;
  }): Project[] {
    // For now, delegate to the async method and handle synchronously
    // This maintains compatibility with existing API routes
    // TODO: Refactor API routes to use async methods
    throw new Error('getUserProjects: Use async getProjects method instead');
  }

  /**
   * Get user project count with optional status filter (API compatibility method)
   */
  getUserProjectCount(userId: number, status?: string): number {
    // For now, delegate to the async method and handle synchronously
    // This maintains compatibility with existing API routes
    // TODO: Refactor API routes to use async methods
    throw new Error('getUserProjectCount: Use async getProjectCount method instead');
  }

  /**
   * Get project count for a user with optional status filter
   */
  async getProjectCount(userId: number, status?: string): Promise<number> {
    // Validate input
    if (!userId || userId <= 0) {
      throw ErrorFactory.invalidInput('userId', userId, 'User ID must be a positive number');
    }

    try {
      const client = await this.pool.connect();
      try {
        let query = `
          SELECT COUNT(DISTINCT p.id) as count
          FROM projects p
          LEFT JOIN project_team_members ptm ON p.id = ptm.project_id
          WHERE p.created_by = $1
             OR p.assigned_to = $1
             OR ptm.user_id = $1
             OR p.visibility = 'public'
        `;

        const params = [userId];

        if (status) {
          query += ' AND p.status = $2';
          params.push(status);
        }

        const result = await client.query(query, params);
        return parseInt(result.rows[0].count);
      } finally {
        client.release();
      }
    } catch (error) {
      console.error('Error getting project count:', error);
      throw ErrorFactory.databaseOperation('count', `Failed to count projects: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create a new task
   */
  async createTask(userId: number, taskData: CreateTaskInput): Promise<Task> {
    const {
      project_id: projectId,
      parent_task_id: parentTaskId,
      title,
      description,
      status = 'todo',
      priority = 'medium',
      due_date: dueDate,
      assigned_to: assignedTo
    } = taskData;

    // Validate and sanitize inputs
    const sanitizedTitle = sanitize(title, 'title');
    const sanitizedDescription = sanitize(description || '', 'description');

    // Validate project exists and user has permission
    const project = await this.getProjectById(projectId);
    if (!(await this.authService.canModifyProject(projectId, userId))) {
      throw ErrorFactory.unauthorized('create task', 'Insufficient permissions to create task in this project');
    }

    // Validate status
    if (!['todo', 'inProgress', 'completed'].includes(status)) {
      throw ErrorFactory.invalidInput('status', status, 'Status must be todo, inProgress, or completed');
    }

    // Validate priority
    if (!['low', 'medium', 'high'].includes(priority)) {
      throw ErrorFactory.invalidInput('priority', priority, 'Priority must be low, medium, or high');
    }

    try {
      // Use transaction for atomic operation
      const taskId = await executeTransaction(async (client: PoolClient) => {
        // Insert task
        const result = await client.query(`
          INSERT INTO tasks (project_id, parent_task_id, title, description, status, priority, due_date, assigned_to, created_by)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
          RETURNING id
        `, [
          projectId,
          parentTaskId || null,
          sanitizedTitle,
          sanitizedDescription,
          status,
          priority,
          dueDate || null,
          assignedTo || null,
          userId
        ]);

        const newTaskId = result.rows[0].id;

        // Log activity
        const activityDescription = parentTaskId
          ? `Subtask "${sanitizedTitle}" created`
          : `Task "${sanitizedTitle}" created`;

        await client.query(`
          INSERT INTO activity_log (project_id, task_id, user_id, activity_type, description)
          VALUES ($1, $2, $3, $4, $5)
        `, [projectId, newTaskId, userId, 'task_creation', activityDescription]);

        return newTaskId;
      });

      // Update project progress outside transaction to prevent locks
      try {
        await this.updateProjectProgressSync(projectId);
      } catch (progressError) {
        console.warn('Failed to update project progress:', progressError);
      }

      // Return the created task
      const createdTask = await this.getTaskById(taskId);
      if (!createdTask) {
        throw ErrorFactory.databaseOperation('create', 'Failed to retrieve created task');
      }
      return createdTask;
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }

      console.error('Error creating task:', error);
      throw ErrorFactory.databaseOperation('create', `Failed to create task: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get task by ID
   */
  async getTaskById(taskId: number): Promise<Task | null> {
    // Validate input
    if (!taskId || taskId <= 0) {
      throw ErrorFactory.invalidInput('taskId', taskId, 'Task ID must be a positive number');
    }

    try {
      const client = await this.pool.connect();
      try {
        const result = await client.query(`
          SELECT t.*, u1.username as created_by_username, u2.username as assigned_to_username
          FROM tasks t
          LEFT JOIN users u1 ON t.created_by = u1.id
          LEFT JOIN users u2 ON t.assigned_to = u2.id
          WHERE t.id = $1
        `, [taskId]);

        if (result.rows.length === 0) {
          return null;
        }

        const taskRow = result.rows[0];
        const task = DataMapper.taskFromDb(taskRow);

        // Get subtasks if this is a main task
        if (!task.parentTaskId) {
          const subtasksResult = await client.query(`
            SELECT t.*, u1.username as created_by_username, u2.username as assigned_to_username
            FROM tasks t
            LEFT JOIN users u1 ON t.created_by = u1.id
            LEFT JOIN users u2 ON t.assigned_to = u2.id
            WHERE t.parent_task_id = $1
            ORDER BY t.created_at ASC
          `, [taskId]);
          task.subtasks = subtasksResult.rows.map(DataMapper.taskFromDb);
        }

        return task;
      } finally {
        client.release();
      }
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }

      console.error('Error getting task by ID:', error);
      throw ErrorFactory.databaseOperation('retrieve', `Failed to retrieve task ${taskId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get all tasks for a project
   */
  async getProjectTasks(projectId: number): Promise<Task[]> {
    // Validate input
    if (!projectId || projectId <= 0) {
      throw ErrorFactory.invalidInput('projectId', projectId, 'Project ID must be a positive number');
    }

    try {
      const client = await this.pool.connect();
      try {
        // Get all tasks for the project in one query to avoid N+1 problem
        const result = await client.query(`
          SELECT t.*, u1.username as created_by_username, u2.username as assigned_to_username
          FROM tasks t
          LEFT JOIN users u1 ON t.created_by = u1.id
          LEFT JOIN users u2 ON t.assigned_to = u2.id
          WHERE t.project_id = $1
          ORDER BY t.parent_task_id IS NULL DESC, t.created_at ASC
        `, [projectId]);

        const allTasks = result.rows.map(DataMapper.taskFromDb);

        // Organize tasks into main tasks with their subtasks
        const mainTasks: Task[] = [];
        const subtaskMap = new Map<number, Task[]>();

        // First pass: separate main tasks and group subtasks
        for (const task of allTasks) {
          if (!task.parentTaskId) {
            // This is a main task
            task.subtasks = [];
            mainTasks.push(task);
          } else {
            // This is a subtask
            if (!subtaskMap.has(task.parentTaskId)) {
              subtaskMap.set(task.parentTaskId, []);
            }
            subtaskMap.get(task.parentTaskId)!.push(task);
          }
        }

        // Second pass: attach subtasks to their parent tasks
        for (const mainTask of mainTasks) {
          if (subtaskMap.has(mainTask.id)) {
            mainTask.subtasks = subtaskMap.get(mainTask.id)!;
          }
        }

        return mainTasks;
      } finally {
        client.release();
      }
    } catch (error) {
      console.error('Error getting project tasks:', error);
      throw ErrorFactory.databaseOperation('retrieve', `Failed to retrieve tasks for project ${projectId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }



  /**
   * Update a task
   */
  async updateTask(taskId: number, userId: number, updateData: Partial<CreateTaskInput>): Promise<Task> {
    // Validate input
    if (!taskId || taskId <= 0) {
      throw ErrorFactory.invalidInput('taskId', taskId, 'Task ID must be a positive number');
    }
    if (!userId || userId <= 0) {
      throw ErrorFactory.invalidInput('userId', userId, 'User ID must be a positive number');
    }

    try {
      const client = await this.pool.connect();
      try {
        // Get current task to check permissions
        const currentTask = await this.getTaskById(taskId);
        if (!currentTask) {
          throw ErrorFactory.notFound('Task', taskId);
        }

        // Check permissions
        if (!(await this.authService.canModifyTask(taskId, userId))) {
          throw ErrorFactory.unauthorized('update task', 'Insufficient permissions to update this task');
        }

        // Build update query dynamically
        const updateFields = [];
        const values = [];
        let paramIndex = 1;

        if (updateData.title !== undefined) {
          updateFields.push(`title = $${paramIndex++}`);
          values.push(sanitize(updateData.title, 'title'));
        }
        if (updateData.description !== undefined) {
          updateFields.push(`description = $${paramIndex++}`);
          values.push(sanitize(updateData.description || '', 'description'));
        }
        if (updateData.status !== undefined) {
          updateFields.push(`status = $${paramIndex++}`);
          values.push(updateData.status);
        }
        if (updateData.priority !== undefined) {
          updateFields.push(`priority = $${paramIndex++}`);
          values.push(updateData.priority);
        }
        if (updateData.due_date !== undefined) {
          updateFields.push(`due_date = $${paramIndex++}`);
          values.push(updateData.due_date);
        }
        if (updateData.assigned_to !== undefined) {
          updateFields.push(`assigned_to = $${paramIndex++}`);
          values.push(updateData.assigned_to);
        }

        if (updateFields.length === 0) {
          return currentTask; // No changes
        }

        updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
        values.push(taskId);

        const query = `
          UPDATE tasks
          SET ${updateFields.join(', ')}
          WHERE id = $${paramIndex}
        `;

        await client.query(query, values);

        // Log activity
        await client.query(`
          INSERT INTO activity_log (project_id, task_id, user_id, activity_type, description)
          VALUES ($1, $2, $3, $4, $5)
        `, [currentTask.projectId, taskId, userId, 'task_update', `Task "${currentTask.title}" updated`]);

        return await this.getTaskById(taskId);
      } finally {
        client.release();
      }
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }
      console.error('Error updating task:', error);
      throw ErrorFactory.databaseOperation('update', `Failed to update task: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete a task
   */
  async deleteTask(taskId: number, userId: number): Promise<boolean> {
    // Validate input
    if (!taskId || taskId <= 0) {
      throw ErrorFactory.invalidInput('taskId', taskId, 'Task ID must be a positive number');
    }
    if (!userId || userId <= 0) {
      throw ErrorFactory.invalidInput('userId', userId, 'User ID must be a positive number');
    }

    try {
      const client = await this.pool.connect();
      try {
        // Get current task to check permissions
        const currentTask = await this.getTaskById(taskId);
        if (!currentTask) {
          throw ErrorFactory.notFound('Task', taskId);
        }

        // Check permissions
        if (!(await this.authService.canDeleteTask(taskId, userId))) {
          throw ErrorFactory.unauthorized('delete task', 'Insufficient permissions to delete this task');
        }

        // Delete task (cascade will handle related records)
        const result = await client.query('DELETE FROM tasks WHERE id = $1', [taskId]);

        if (result.rowCount === 0) {
          return false;
        }

        // Log activity
        await client.query(`
          INSERT INTO activity_log (project_id, user_id, activity_type, description)
          VALUES ($1, $2, $3, $4)
        `, [currentTask.projectId, userId, 'task_deletion', `Task "${currentTask.title}" deleted`]);

        return true;
      } finally {
        client.release();
      }
    } catch (error) {
      if (isTaskMasterError(error)) {
        throw error;
      }
      console.error('Error deleting task:', error);
      throw ErrorFactory.databaseOperation('delete', `Failed to delete task: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get project files with pagination
   */
  async getProjectFiles(projectId: number, options?: {
    limit?: number;
    offset?: number;
  }): Promise<ProjectFile[]> {
    const { limit = 20, offset = 0 } = options || {};

    try {
      const client = await this.pool.connect();
      try {
        const result = await client.query(`
          SELECT pf.*, u.username as uploaded_by_username
          FROM project_files pf
          JOIN users u ON pf.uploaded_by = u.id
          WHERE pf.project_id = $1
          ORDER BY pf.uploaded_at DESC
          LIMIT $2 OFFSET $3
        `, [projectId, limit, offset]);

        return result.rows as ProjectFile[];
      } finally {
        client.release();
      }
    } catch (error) {
      console.error('Error getting project files:', error);
      return [];
    }
  }

  /**
   * Get project comments with pagination
   */
  async getProjectComments(projectId: number, options?: {
    limit?: number;
    offset?: number;
  }): Promise<ProjectComment[]> {
    const { limit = 20, offset = 0 } = options || {};

    try {
      const client = await this.pool.connect();
      try {
        const result = await client.query(`
          SELECT pc.*, u.username
          FROM project_comments pc
          JOIN users u ON pc.user_id = u.id
          WHERE pc.project_id = $1
          ORDER BY pc.created_at DESC
          LIMIT $2 OFFSET $3
        `, [projectId, limit, offset]);

        return result.rows as ProjectComment[];
      } finally {
        client.release();
      }
    } catch (error) {
      console.error('Error getting project comments:', error);
      return [];
    }
  }

  /**
   * Get dashboard activity for a user
   */
  async getDashboardActivity(userId: number, limit: number = 10): Promise<ActivityLogEntry[]> {
    // Validate input
    if (!userId || userId <= 0) {
      throw ErrorFactory.invalidInput('userId', userId, 'User ID must be a positive number');
    }

    try {
      const client = await this.pool.connect();
      try {
        // Get recent activity from projects the user has access to
        const result = await client.query(`
          SELECT DISTINCT al.*, p.title as project_title, u.username as user_username
          FROM activity_log al
          JOIN projects p ON al.project_id = p.id
          JOIN users u ON al.user_id = u.id
          LEFT JOIN project_team_members ptm ON p.id = ptm.project_id
          WHERE p.created_by = $1
             OR p.assigned_to = $1
             OR ptm.user_id = $1
             OR p.visibility = 'public'
          ORDER BY al.created_at DESC
          LIMIT $2
        `, [userId, limit]);

        return result.rows.map(DataMapper.activityLogFromDb);
      } finally {
        client.release();
      }
    } catch (error) {
      console.error('Error getting dashboard activity:', error);
      return [];
    }
  }

  /**
   * Get project activity log
   */
  async getProjectActivity(projectId: number, options?: {
    limit?: number;
    offset?: number;
  }): Promise<ActivityLogEntry[]> {
    const { limit = 50, offset = 0 } = options || {};

    try {
      const client = await this.pool.connect();
      try {
        const result = await client.query(`
          SELECT al.*, u.username
          FROM activity_log al
          JOIN users u ON al.user_id = u.id
          WHERE al.project_id = $1
          ORDER BY al.created_at DESC
          LIMIT $2 OFFSET $3
        `, [projectId, limit, offset]);

        return result.rows as ActivityLogEntry[];
      } finally {
        client.release();
      }
    } catch (error) {
      console.error('Error getting project activity:', error);
      return [];
    }
  }

  /**
   * Update project progress synchronously
   */
  async updateProjectProgressSync(projectId: number): Promise<void> {
    try {
      const client = await this.pool.connect();
      try {
        // Calculate progress based on completed tasks
        const result = await client.query(`
          SELECT
            COUNT(*) as total,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed
          FROM tasks WHERE project_id = $1
        `, [projectId]);

        const { total, completed } = result.rows[0];
        const progress = total > 0 ? Math.round((completed / total) * 100) : 0;

        // Update project progress
        await client.query(`
          UPDATE projects
          SET progress = $1, updated_at = CURRENT_TIMESTAMP
          WHERE id = $2
        `, [progress, projectId]);
      } finally {
        client.release();
      }
    } catch (error) {
      console.error('Error updating project progress:', error);
      throw error;
    }
  }
}
