import { getDatabase } from './database';
import { UserService } from './user-service';

/**
 * Separate initialization service to avoid circular dependencies
 * This should be called manually after the application starts
 */
export class DatabaseInitService {
  private static initialized = false;

  /**
   * Initialize default users if they don't exist
   * This is separated from the main database initialization to avoid circular dependencies
   */
  static async initializeDefaultUsers(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      console.log('🔧 Checking for default users...');
      
      // Get database connection
      const pool = getDatabase();
      
      // Initialize user service
      const userService = new UserService();
      
      // Check if users exist
      const client = await pool.connect();
      try {
        const result = await client.query('SELECT COUNT(*) as count FROM users');
        const userCount = parseInt(result.rows[0].count);
        
        if (userCount === 0) {
          console.log('🔧 No users found, creating default users...');
          
          // Create default admin user
          const adminUser = await userService.createUser({
            email: "<EMAIL>",
            username: "admin",
            password: "password",
            role: "admin"
          });
          console.log('✅ Created admin user:', adminUser.email);

          // Create default moderator user
          const moderatorUser = await userService.createUser({
            email: "<EMAIL>", 
            username: "moderator",
            password: "moderator123",
            role: "moderator"
          });
          console.log('✅ Created moderator user:', moderatorUser.email);

          // Create default regular user
          const regularUser = await userService.createUser({
            email: "<EMAIL>",
            username: "user", 
            password: "user123",
            role: "user"
          });
          console.log('✅ Created regular user:', regularUser.email);

          console.log('🎉 Default users initialized successfully!');
        } else {
          console.log(`✅ Found ${userCount} existing users, skipping initialization`);
        }
      } finally {
        client.release();
      }

      this.initialized = true;
    } catch (error) {
      console.error('❌ Failed to initialize default users:', error);
      throw error;
    }
  }

  /**
   * Reset the initialization flag (for testing)
   */
  static reset(): void {
    this.initialized = false;
  }
}
