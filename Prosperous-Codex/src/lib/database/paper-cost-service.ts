import { getDatabase } from './database';
import { Pool } from 'pg';

export interface UserPaperOption {
  id: number;
  user_id: number;
  category: 'Inner Text' | 'Cover' | 'Endpapers';
  paper_id: string;
  name: string;
  source: string;
  sheet_height?: number;
  sheet_width: number;
  grain_direction: string;
  caliper: number;
  cost_per_ream?: number;
  gsm: number;
  cost_per_ton?: number;
  is_custom: boolean;
  created_at: string;
  updated_at: string;
}

export interface SavedCalculation {
  id: number;
  user_id: number;
  project_name: string;
  tab_id: 'innerText' | 'cover' | 'endpapers';
  job_inputs: string; // JSON string
  selected_paper_id?: string;
  calculation_results?: string; // JSON string
  created_at: string;
  updated_at: string;
}

export interface UserSelection {
  id: number;
  user_id: number;
  component_type: 'innerText' | 'cover' | 'endpapers';
  component_id: string;
  component_data: string; // JSON string
  created_at: string;
}

export interface CalculationHistory {
  id: number;
  user_id: number;
  tab_id: string;
  job_inputs: string; // JSON string
  paper_options: string; // JSON string
  results: string; // JSON string
  created_at: string;
}

export class PaperCostService {
  private pool = getDatabase();

  /**
   * Get user's paper options by category
   */
  async getUserPaperOptions(userId: number, category?: string): Promise<UserPaperOption[]> {
    try {
      const client = await this.pool.connect();
      try {
        let query = `
          SELECT * FROM user_paper_options
          WHERE user_id = $1
        `;
        const params: (string | number)[] = [userId];

        if (category) {
          query += ' AND category = $2';
          params.push(category);
        }

        query += ' ORDER BY created_at DESC';

        const result = await client.query(query, params);
        return result.rows as UserPaperOption[];
      } finally {
        client.release();
      }
    } catch (error) {
      console.error('Error getting user paper options:', error);
      return [];
    }
  }

  /**
   * Add custom paper option
   */
  async addPaperOption(userId: number, paperData: Omit<UserPaperOption, 'id' | 'user_id' | 'created_at' | 'updated_at'>): Promise<UserPaperOption | null> {
    try {
      const client = await this.pool.connect();
      try {
        const result = await client.query(`
          INSERT INTO user_paper_options (
            user_id, category, paper_id, name, source, sheet_height, sheet_width,
            grain_direction, caliper, cost_per_ream, gsm, cost_per_ton, is_custom
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
          RETURNING *
        `, [
          userId,
          paperData.category,
          paperData.paper_id,
          paperData.name,
          paperData.source,
          paperData.sheet_height || null,
          paperData.sheet_width,
          paperData.grain_direction,
          paperData.caliper,
          paperData.cost_per_ream || null,
          paperData.gsm,
          paperData.cost_per_ton || null,
          paperData.is_custom
        ]);

        return result.rows[0] as UserPaperOption;
      } finally {
        client.release();
      }
    } catch (error) {
      console.error('Error adding paper option:', error);
      return null;
    }
  }

  /**
   * Save calculation
   */
  async saveCalculation(userId: number, calculationData: Omit<SavedCalculation, 'id' | 'user_id' | 'created_at' | 'updated_at'>): Promise<SavedCalculation | null> {
    try {
      const client = await this.pool.connect();
      try {
        const result = await client.query(`
          INSERT INTO saved_calculations (user_id, project_name, tab_id, job_inputs, selected_paper_id, calculation_results)
          VALUES ($1, $2, $3, $4, $5, $6)
          RETURNING *
        `, [
          userId,
          calculationData.project_name,
          calculationData.tab_id,
          calculationData.job_inputs,
          calculationData.selected_paper_id || null,
          calculationData.calculation_results || null
        ]);

        return result.rows[0] as SavedCalculation;
      } finally {
        client.release();
      }
    } catch (error) {
      console.error('Error saving calculation:', error);
      return null;
    }
  }

  /**
   * Get saved calculations
   */
  getSavedCalculations(userId: number): SavedCalculation[] {
    try {
      const stmt = this.db.prepare(`
        SELECT * FROM saved_calculations 
        WHERE user_id = ? 
        ORDER BY updated_at DESC
      `);

      return stmt.all(userId) as SavedCalculation[];
    } catch (error) {
      console.error('Error getting saved calculations:', error);
      return [];
    }
  }

  /**
   * Update user selection (shopping cart)
   */
  updateUserSelection(userId: number, componentType: string, componentId: string, componentData: Record<string, unknown>): boolean {
    try {
      const stmt = this.db.prepare(`
        INSERT OR REPLACE INTO user_selections (user_id, component_type, component_id, component_data)
        VALUES (?, ?, ?, ?)
      `);

      const result = stmt.run(
        userId,
        componentType,
        componentId,
        JSON.stringify(componentData)
      );

      return result.changes > 0;
    } catch (error) {
      console.error('Error updating user selection:', error);
      return false;
    }
  }

  /**
   * Get user selections (shopping cart)
   */
  getUserSelections(userId: number): UserSelection[] {
    try {
      const stmt = this.db.prepare(`
        SELECT * FROM user_selections 
        WHERE user_id = ? 
        ORDER BY created_at DESC
      `);

      return stmt.all(userId) as UserSelection[];
    } catch (error) {
      console.error('Error getting user selections:', error);
      return [];
    }
  }

  /**
   * Remove user selection
   */
  removeUserSelection(userId: number, componentType: string, componentId: string): boolean {
    try {
      const stmt = this.db.prepare(`
        DELETE FROM user_selections 
        WHERE user_id = ? AND component_type = ? AND component_id = ?
      `);

      const result = stmt.run(userId, componentType, componentId);
      return result.changes > 0;
    } catch (error) {
      console.error('Error removing user selection:', error);
      return false;
    }
  }

  /**
   * Add calculation to history
   */
  addCalculationHistory(userId: number, historyData: Omit<CalculationHistory, 'id' | 'user_id' | 'created_at'>): boolean {
    try {
      const stmt = this.db.prepare(`
        INSERT INTO calculation_history (user_id, tab_id, job_inputs, paper_options, results)
        VALUES (?, ?, ?, ?, ?)
      `);

      const result = stmt.run(
        userId,
        historyData.tab_id,
        historyData.job_inputs,
        historyData.paper_options,
        historyData.results
      );

      return result.changes > 0;
    } catch (error) {
      console.error('Error adding calculation history:', error);
      return false;
    }
  }

  /**
   * Get calculation history
   */
  getCalculationHistory(userId: number, limit: number = 50): CalculationHistory[] {
    try {
      const stmt = this.db.prepare(`
        SELECT * FROM calculation_history 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT ?
      `);

      return stmt.all(userId, limit) as CalculationHistory[];
    } catch (error) {
      console.error('Error getting calculation history:', error);
      return [];
    }
  }

  /**
   * Delete saved calculation
   */
  deleteCalculation(userId: number, calculationId: number): boolean {
    try {
      const stmt = this.db.prepare(`
        DELETE FROM saved_calculations 
        WHERE id = ? AND user_id = ?
      `);

      const result = stmt.run(calculationId, userId);
      return result.changes > 0;
    } catch (error) {
      console.error('Error deleting calculation:', error);
      return false;
    }
  }

  /**
   * Delete paper option
   */
  deletePaperOption(userId: number, paperOptionId: number): boolean {
    try {
      const stmt = this.db.prepare(`
        DELETE FROM user_paper_options 
        WHERE id = ? AND user_id = ? AND is_custom = 1
      `);

      const result = stmt.run(paperOptionId, userId);
      return result.changes > 0;
    } catch (error) {
      console.error('Error deleting paper option:', error);
      return false;
    }
  }
}
