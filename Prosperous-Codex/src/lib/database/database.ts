import { Pool, PoolClient } from 'pg';
import { readFileSync } from 'fs';
import { join } from 'path';
import { applyMigrations } from './migrations';

// Database configuration
const SCHEMA_PATH = join(process.cwd(), 'src', 'lib', 'database', 'postgres-schema.sql');

// Global database pool instance - following Node-Postgres best practices
let pool: Pool | null = null;

/**
 * Initialize the database connection pool
 * Following Node-Postgres best practices for connection pooling
 */
function createPool(): Pool {
  if (!process.env.POSTGRES_URL) {
    throw new Error('POSTGRES_URL environment variable is required');
  }

  const newPool = new Pool({
    connectionString: process.env.POSTGRES_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
  });

  // Add error handler to prevent uncaught exceptions
  newPool.on('error', (err, client) => {
    console.error('Unexpected error on idle client', err);
    // Don't exit the process, just log the error
  });

  return newPool;
}

/**
 * Initialize database schema
 */
async function initializeSchema(): Promise<void> {
  if (!pool) {
    throw new Error('Database pool not initialized');
  }

  try {
    const client = await pool.connect();

    try {
      // Check if this is a fresh database
      const result = await client.query(`
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public' AND table_name = 'users'
      `);

      const isNewDatabase = result.rows.length === 0;

      if (isNewDatabase) {
        // Fresh database - execute full schema
        console.log('🔧 Initializing fresh database...');
        const schema = readFileSync(SCHEMA_PATH, 'utf-8');
        await client.query(schema);
      } else {
        // Existing database - only apply migrations
        console.log('🔧 Existing database detected, applying migrations...');
      }

      // Apply any pending migrations
      try {
        await applyMigrations(client);
      } catch (error) {
        console.warn('⚠️ Failed to apply migrations:', error);
      }
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Failed to initialize schema:', error);
    throw error;
  }
}

/**
 * Get the database pool instance
 * Completely lazy initialization to avoid any build-time issues
 */
export function getDatabase(): Pool {
  // Return existing pool if available
  if (pool) {
    return pool;
  }

  // Completely skip database initialization during build time or when no URL is provided
  if (!process.env.POSTGRES_URL || process.env.NODE_ENV === undefined) {
    console.warn('⚠️ Database not configured, returning mock pool');
    // Return a mock pool that will fail gracefully at runtime
    return {
      connect: () => Promise.reject(new Error('Database not configured - POSTGRES_URL missing')),
      query: () => Promise.reject(new Error('Database not configured - POSTGRES_URL missing')),
      end: () => Promise.resolve(),
      on: () => {},
    } as any;
  }

  try {
    // Create the pool only when actually needed
    pool = createPool();

    // Only initialize schema in runtime, never during build
    // Remove all automatic initialization to avoid circular dependencies
    if (typeof window === 'undefined' && process.env.NODE_ENV === 'development') {
      // Defer initialization to avoid build-time issues
      setTimeout(async () => {
        try {
          await initializeSchema();
          console.log('✅ Database schema initialized');
        } catch (error) {
          console.warn('⚠️ Failed to initialize database schema:', error);
        }
      }, 2000); // Delay initialization by 2 seconds
    }

    return pool;
  } catch (error) {
    console.error('Failed to create database pool:', error);
    // Return mock pool on error
    return {
      connect: () => Promise.reject(new Error('Database pool creation failed')),
      query: () => Promise.reject(new Error('Database pool creation failed')),
      end: () => Promise.resolve(),
      on: () => {},
    } as any;
  }
}

/**
 * Close the database connection
 */
export async function closeDatabase(): Promise<void> {
  if (pool) {
    await pool.end();
    pool = null;
  }
}

/**
 * Force close and reset database connection (for database reset operations)
 */
export async function forceResetDatabase(): Promise<void> {
  if (pool) {
    try {
      await pool.end();
    } catch (error) {
      console.warn('⚠️ Error closing database connection:', error);
    }
    pool = null;
  }
  console.log('🔄 Database connection reset');
}

/**
 * Execute a transaction
 */
export async function executeTransaction<T>(callback: (client: PoolClient) => Promise<T>): Promise<T> {
  const database = getDatabase();
  const client = await database.connect();

  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Database health check
 */
export async function healthCheck(): Promise<boolean> {
  try {
    const database = getDatabase();
    const client = await database.connect();
    try {
      const result = await client.query('SELECT 1 as health');
      return result.rows.length > 0 && result.rows[0].health === 1;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Health check failed:', error);
    return false;
  }
}

/**
 * Get database statistics
 */
export async function getDatabaseStats() {
  try {
    const database = getDatabase();
    const client = await database.connect();

    try {
      const userCount = await client.query('SELECT COUNT(*) as count FROM users');
      const projectCount = await client.query('SELECT COUNT(*) as count FROM projects');
      const activeSessionCount = await client.query("SELECT COUNT(*) as count FROM user_sessions WHERE expires_at > NOW()");

      return {
        users: parseInt(userCount.rows[0].count),
        projects: parseInt(projectCount.rows[0].count),
        activeSessions: parseInt(activeSessionCount.rows[0].count),
        healthy: true
      };
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Database stats failed:', error);
    return {
      users: 0,
      projects: 0,
      activeSessions: 0,
      healthy: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Database manager for compatibility
const databaseManager = {
  get: getDatabase,
  close: closeDatabase,
  forceReset: forceResetDatabase,
  transaction: executeTransaction,
  healthCheck,
  stats: getDatabaseStats
};

export default databaseManager;
