import { Pool, PoolClient } from 'pg';
import { readFileSync } from 'fs';
import { join } from 'path';
import { applyMigrations } from './migrations';
import { initializeApp } from './init';

// Database configuration
const SCHEMA_PATH = join(process.cwd(), 'src', 'lib', 'database', 'postgres-schema.sql');

// Database pool instance
let pool: Pool | null = null;

/**
 * Initialize the database connection and create tables
 */
export function initializeDatabase(): Pool {
  if (pool) {
    return pool;
  }

  try {
    // Create database connection pool
    pool = new Pool({
      connectionString: process.env.POSTGRES_URL,
      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    });

    // Test connection and initialize schema
    initializeSchema();

    return pool;
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
}

/**
 * Initialize database schema
 */
async function initializeSchema(): Promise<void> {
  if (!pool) {
    throw new Error('Database pool not initialized');
  }

  try {
    const client = await pool.connect();

    try {
      // Check if this is a fresh database
      const result = await client.query(`
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public' AND table_name = 'users'
      `);

      const isNewDatabase = result.rows.length === 0;

      if (isNewDatabase) {
        // Fresh database - execute full schema
        console.log('🔧 Initializing fresh database...');
        const schema = readFileSync(SCHEMA_PATH, 'utf-8');
        await client.query(schema);
      } else {
        // Existing database - only apply migrations
        console.log('🔧 Existing database detected, applying migrations...');
      }

      // Apply any pending migrations
      try {
        await applyMigrations(client);
      } catch (error) {
        console.warn('⚠️ Failed to apply migrations:', error);
      }
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Failed to initialize schema:', error);
    throw error;
  }
}

/**
 * Get the database pool instance
 */
export function getDatabase(): Pool {
  if (!pool) {
    const database = initializeDatabase();

    // Initialize default users in development if they don't exist
    if (process.env.NODE_ENV === 'development') {
      setImmediate(async () => {
        try {
          const client = await database.connect();
          try {
            const result = await client.query('SELECT COUNT(*) as count FROM users');
            const userCount = parseInt(result.rows[0].count);
            if (userCount === 0) {
              console.log('🔧 No users found, initializing default users...');
              await initializeApp();
            }
          } finally {
            client.release();
          }
        } catch (error) {
          console.warn('⚠️ Failed to check user count:', error);
        }
      });
    }

    return database;
  }
  return pool;
}

/**
 * Close the database connection
 */
export async function closeDatabase(): Promise<void> {
  if (pool) {
    await pool.end();
    pool = null;
  }
}

/**
 * Force close and reset database connection (for database reset operations)
 */
export async function forceResetDatabase(): Promise<void> {
  if (pool) {
    try {
      await pool.end();
    } catch (error) {
      console.warn('⚠️ Error closing database connection:', error);
    }
    pool = null;
  }
  console.log('🔄 Database connection reset');
}

/**
 * Execute a transaction
 */
export async function executeTransaction<T>(callback: (client: PoolClient) => Promise<T>): Promise<T> {
  const database = getDatabase();
  const client = await database.connect();

  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Database health check
 */
export function healthCheck(): boolean {
  try {
    const database = getDatabase();
    const result = database.prepare('SELECT 1 as health').get() as { health: number } | undefined;
    return result ? result.health === 1 : false;
  } catch {
    return false;
  }
}

/**
 * Get database statistics
 */
export function getDatabaseStats() {
  try {
    const database = getDatabase();
    
    const userCount = database.prepare('SELECT COUNT(*) as count FROM users').get() as { count: number };
    const projectCount = database.prepare('SELECT COUNT(*) as count FROM projects').get() as { count: number };
    const activeSessionCount = database.prepare("SELECT COUNT(*) as count FROM user_sessions WHERE expires_at > datetime('now')").get() as { count: number };
    
    return {
      users: userCount.count,
      projects: projectCount.count,
      activeSessions: activeSessionCount.count,
      healthy: true
    };
  } catch (error) {
    return {
      users: 0,
      projects: 0,
      activeSessions: 0,
      healthy: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Initialize database on module load
initializeDatabase();

const databaseManager = {
  initialize: initializeDatabase,
  get: getDatabase,
  close: closeDatabase,
  forceReset: forceResetDatabase,
  transaction: executeTransaction,
  healthCheck,
  stats: getDatabaseStats
};

export default databaseManager;
