import { getDatabase } from './database';
import bcrypt from 'bcryptjs';
import { User } from '@/lib/types/auth';

export interface DatabaseUser {
  id: number;
  email: string;
  username?: string;
  password_hash: string;
  role: 'user' | 'moderator' | 'admin';
  language?: string;
  created_at: string;
  updated_at: string;
  last_login?: string;
  is_active: boolean;
}

export interface CreateUserData {
  email: string;
  username?: string;
  password: string;
  role?: 'user' | 'moderator' | 'admin';
}

export interface UpdateUserData {
  username?: string;
  role?: 'user' | 'moderator' | 'admin';
  language?: string;
  is_active?: boolean;
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
}

export class UserService {
  private pool = getDatabase();

  /**
   * Create a new user
   */
  async createUser(userData: CreateUserData): Promise<User> {
    const { email, username, password, role = 'user' } = userData;
    
    // Hash password
    const password_hash = await bcrypt.hash(password, 12);
    
    try {
      const client = await this.pool.connect();
      try {
        const result = await client.query(`
          INSERT INTO users (email, username, password_hash, role)
          VALUES ($1, $2, $3, $4)
          RETURNING id
        `, [email, username || null, password_hash, role]);
        
        const userId = result.rows[0].id;
        
        // Get the created user
        const user = await this.getUserById(userId);
        if (!user) {
          throw new Error('Failed to create user');
        }
        
        return user;
      } finally {
        client.release();
      }
    } catch (error: any) {
      if (error.code === '23505') { // Unique constraint violation
        throw new Error('User with this email already exists');
      }
      throw error;
    }
  }

  /**
   * Get user by ID
   */
  async getUserById(id: number): Promise<User | null> {
    try {
      const client = await this.pool.connect();
      try {
        const result = await client.query(`
          SELECT id, email, username, role, created_at, last_login, is_active
          FROM users
          WHERE id = $1 AND is_active = true
        `, [id]);

        if (result.rows.length === 0) {
          return null;
        }

        const user = result.rows[0];
        return {
          id: user.id.toString(),
          email: user.email,
          username: user.username,
          createdAt: user.created_at,
          role: user.role,
          isActive: user.is_active
        };
      } finally {
        client.release();
      }
    } catch (error) {
      console.error('Error getting user by ID:', error);
      return null;
    }
  }

  /**
   * Get user by email
   */
  async getUserByEmail(email: string): Promise<User | null> {
    try {
      const client = await this.pool.connect();
      try {
        const result = await client.query(`
          SELECT id, email, username, role, created_at, last_login, is_active
          FROM users
          WHERE email = $1 AND is_active = true
        `, [email]);

        if (result.rows.length === 0) {
          return null;
        }

        const user = result.rows[0];
        return {
          id: user.id.toString(),
          email: user.email,
          username: user.username,
          createdAt: user.created_at,
          role: user.role,
          isActive: user.is_active
        };
      } finally {
        client.release();
      }
    } catch (error) {
      console.error('Error getting user by email:', error);
      return null;
    }
  }

  /**
   * Authenticate user with email and password
   */
  async authenticateUser(email: string, password: string): Promise<User | null> {
    try {
      const client = await this.pool.connect();
      try {
        const result = await client.query(`
          SELECT id, email, username, password_hash, role, created_at, last_login, is_active
          FROM users
          WHERE email = $1 AND is_active = true
        `, [email]);

        if (result.rows.length === 0) {
          return null;
        }

        const user = result.rows[0];

        // Verify password
        const isValidPassword = await bcrypt.compare(password, user.password_hash);

        if (!isValidPassword) {
          return null;
        }

        // Update last login
        await this.updateLastLogin(user.id);

        return {
          id: user.id.toString(),
          email: user.email,
          username: user.username,
          createdAt: user.created_at,
          role: user.role
        };
      } finally {
        client.release();
      }
    } catch (error) {
      console.error('Error authenticating user:', error);
      return null;
    }
  }

  /**
   * Update user
   */
  async updateUser(id: number, updateData: UpdateUserData): Promise<boolean> {
    try {
      const client = await this.pool.connect();
      try {
        const setParts: string[] = [];
        const values: any[] = [];
        let paramIndex = 1;

        if (updateData.username !== undefined) {
          setParts.push(`username = $${paramIndex++}`);
          values.push(updateData.username);
        }

        if (updateData.role !== undefined) {
          setParts.push(`role = $${paramIndex++}`);
          values.push(updateData.role);
        }

        if (updateData.language !== undefined) {
          setParts.push(`language = $${paramIndex++}`);
          values.push(updateData.language);
        }

        if (updateData.is_active !== undefined) {
          setParts.push(`is_active = $${paramIndex++}`);
          values.push(updateData.is_active);
        }

        if (setParts.length === 0) {
          return false;
        }

        setParts.push(`updated_at = CURRENT_TIMESTAMP`);
        values.push(id);

        const query = `
          UPDATE users 
          SET ${setParts.join(', ')}
          WHERE id = $${paramIndex}
        `;

        const result = await client.query(query, values);
        return result.rowCount > 0;
      } finally {
        client.release();
      }
    } catch (error) {
      console.error('Error updating user:', error);
      return false;
    }
  }

  /**
   * Change user password
   */
  async changePassword(id: number, passwordData: ChangePasswordData): Promise<boolean> {
    try {
      const client = await this.pool.connect();
      try {
        // First verify current password
        const userResult = await client.query(`
          SELECT password_hash FROM users WHERE id = $1
        `, [id]);

        if (userResult.rows.length === 0) {
          return false;
        }

        const isValidPassword = await bcrypt.compare(
          passwordData.currentPassword, 
          userResult.rows[0].password_hash
        );

        if (!isValidPassword) {
          return false;
        }

        // Hash new password
        const newPasswordHash = await bcrypt.hash(passwordData.newPassword, 12);

        // Update password
        const result = await client.query(`
          UPDATE users 
          SET password_hash = $1, updated_at = CURRENT_TIMESTAMP
          WHERE id = $2
        `, [newPasswordHash, id]);

        return result.rowCount > 0;
      } finally {
        client.release();
      }
    } catch (error) {
      console.error('Error changing password:', error);
      return false;
    }
  }

  /**
   * Update last login timestamp
   */
  private async updateLastLogin(id: number): Promise<void> {
    try {
      const client = await this.pool.connect();
      try {
        await client.query(`
          UPDATE users
          SET last_login = CURRENT_TIMESTAMP
          WHERE id = $1
        `, [id]);
      } finally {
        client.release();
      }
    } catch (error) {
      // Silent fail for non-critical operation
      console.warn('Failed to update last login:', error);
    }
  }

  /**
   * Get all users (admin function)
   */
  async getAllUsers(): Promise<User[]> {
    try {
      const client = await this.pool.connect();
      try {
        const result = await client.query(`
          SELECT id, email, username, role, created_at, last_login, is_active
          FROM users 
          ORDER BY created_at DESC
        `);

        return result.rows.map(user => ({
          id: user.id.toString(),
          email: user.email,
          username: user.username,
          createdAt: user.created_at,
          role: user.role,
          isActive: user.is_active
        }));
      } finally {
        client.release();
      }
    } catch (error) {
      console.error('Error getting all users:', error);
      return [];
    }
  }

  /**
   * Refresh database connection (for compatibility)
   */
  refreshConnection(): void {
    this.pool = getDatabase();
  }
}
