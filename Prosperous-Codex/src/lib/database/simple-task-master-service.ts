import { getDatabase } from './database';
import { Pool, PoolClient } from 'pg';

// Simplified types for camelCase API - no middleware translation needed
interface SimpleProject {
  id: number;
  title: string;
  description: string;
  fullDescription?: string;
  eventLog?: string;
  status: 'todo' | 'inProgress' | 'completed';
  priority: 'low' | 'medium' | 'high';
  progress?: number;
  dueDate?: string;
  assignedTo?: number;
  visibility: 'public' | 'private';
  createdBy: number;
  createdAt: string;
  updatedAt: string;
  tasks?: SimpleTask[];
}

interface SimpleTask {
  id: number;
  projectId: number;
  title: string;
  description: string;
  status: 'todo' | 'inProgress' | 'completed';
  priority: 'low' | 'medium' | 'high';
  dueDate?: string;
  assignedTo?: number;
  createdBy: number;
  createdAt: string;
  updatedAt: string;
}

interface CreateProjectInput {
  title: string;
  description: string;
  fullDescription?: string;
  eventLog?: string;
  status?: 'todo' | 'inProgress' | 'completed';
  priority?: 'low' | 'medium' | 'high';
  progress?: number;
  dueDate?: string;
  assignedTo?: number;
  visibility?: 'public' | 'private';
}

interface CreateTaskInput {
  projectId: number;
  title: string;
  description: string;
  status?: 'todo' | 'inProgress' | 'completed';
  priority?: 'low' | 'medium' | 'high';
  dueDate?: string;
  assignedTo?: number;
}

export class SimpleTaskMasterService {
  private pool = getDatabase();

  /**
   * Create a new project - simplified version without complex middleware
   */
  async createProject(userId: number, projectData: CreateProjectInput): Promise<SimpleProject> {
    console.log('🔨 SimpleTaskMasterService.createProject called with:', {
      userId,
      projectData: JSON.stringify(projectData, null, 2)
    });

    // Basic validation
    if (!userId || userId <= 0) {
      throw new Error('User ID must be a positive number');
    }
    if (!projectData.title || projectData.title.trim().length === 0) {
      throw new Error('Project title is required');
    }

    // Set defaults
    const {
      title,
      description = '',
      fullDescription = '',
      eventLog = '',
      status = 'todo',
      priority = 'medium',
      progress = 0,
      dueDate,
      assignedTo,
      visibility = 'public'
    } = projectData;

    console.log('📝 Processed project data:', {
      title, description, fullDescription, eventLog, status, priority, progress, dueDate, assignedTo, visibility
    });

    try {
      const client = await this.pool.connect();
      console.log('✅ Database connected');

      try {
        await client.query('BEGIN');
        console.log('🔄 Transaction started');

        // Insert project - handle snake_case to camelCase conversion at SQL level
        const projectResult = await client.query(`
          INSERT INTO projects (
            title, description, full_description, event_log, status, priority, 
            progress, due_date, visibility, created_by, assigned_to, created_at, updated_at
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, NOW(), NOW())
          RETURNING id, title, description, full_description, event_log, status, priority, 
                   progress, due_date, visibility, created_by, assigned_to, created_at, updated_at
        `, [
          title.trim(),
          description.trim(),
          fullDescription.trim(),
          eventLog.trim(),
          status,
          priority,
          progress,
          dueDate || null,
          visibility,
          userId,
          assignedTo || null
        ]);

        console.log('✅ Project inserted, result:', projectResult.rows[0]);

        await client.query('COMMIT');
        console.log('✅ Transaction committed');

        const row = projectResult.rows[0];
        
        // Convert snake_case database fields to camelCase API response
        const project: SimpleProject = {
          id: row.id,
          title: row.title,
          description: row.description,
          fullDescription: row.full_description,
          eventLog: row.event_log,
          status: row.status,
          priority: row.priority,
          progress: row.progress,
          dueDate: row.due_date,
          assignedTo: row.assigned_to,
          visibility: row.visibility,
          createdBy: row.created_by,
          createdAt: row.created_at,
          updatedAt: row.updated_at
        };

        console.log('🎉 Project created successfully:', project);
        return project;

      } catch (error) {
        await client.query('ROLLBACK');
        console.error('❌ Transaction rolled back due to error:', error);
        throw error;
      } finally {
        client.release();
        console.log('🔌 Database connection released');
      }
    } catch (error) {
      console.error('❌ Database error in createProject:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        error: error
      });
      throw new Error(`Failed to create project: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get projects for a user - simplified version
   */
  async getProjects(userId: number, includeDetails: boolean = false): Promise<SimpleProject[]> {
    console.log('🔍 SimpleTaskMasterService.getProjects called with:', { userId, includeDetails });

    if (!userId || userId <= 0) {
      throw new Error('User ID must be a positive number');
    }

    try {
      const client = await this.pool.connect();
      console.log('✅ Database connected');

      try {
        // Get projects where user is creator, assigned, or has public visibility
        const projectsResult = await client.query(`
          SELECT id, title, description, full_description, event_log, status, priority, 
                 progress, due_date, visibility, created_by, assigned_to, created_at, updated_at
          FROM projects 
          WHERE created_by = $1 OR assigned_to = $1 OR visibility = 'public'
          ORDER BY updated_at DESC
        `, [userId]);

        console.log('📋 Projects query result:', projectsResult.rows.length, 'rows');

        const projects: SimpleProject[] = projectsResult.rows.map(row => ({
          id: row.id,
          title: row.title,
          description: row.description,
          fullDescription: row.full_description,
          eventLog: row.event_log,
          status: row.status,
          priority: row.priority,
          progress: row.progress,
          dueDate: row.due_date,
          assignedTo: row.assigned_to,
          visibility: row.visibility,
          createdBy: row.created_by,
          createdAt: row.created_at,
          updatedAt: row.updated_at
        }));

        console.log('✅ Projects converted successfully:', projects.length, 'projects');
        return projects;

      } finally {
        client.release();
        console.log('🔌 Database connection released');
      }
    } catch (error) {
      console.error('❌ Database error in getProjects:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        error: error
      });
      throw new Error(`Failed to get projects: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create a new task - simplified version
   */
  async createTask(userId: number, taskData: CreateTaskInput): Promise<SimpleTask> {
    console.log('🔨 SimpleTaskMasterService.createTask called with:', {
      userId,
      taskData: JSON.stringify(taskData, null, 2)
    });

    // Basic validation
    if (!userId || userId <= 0) {
      throw new Error('User ID must be a positive number');
    }
    if (!taskData.title || taskData.title.trim().length === 0) {
      throw new Error('Task title is required');
    }
    if (!taskData.projectId || taskData.projectId <= 0) {
      throw new Error('Project ID is required');
    }

    // Set defaults
    const {
      projectId,
      title,
      description = '',
      status = 'todo',
      priority = 'medium',
      dueDate,
      assignedTo
    } = taskData;

    try {
      const client = await this.pool.connect();
      console.log('✅ Database connected');

      try {
        await client.query('BEGIN');
        console.log('🔄 Transaction started');

        // Insert task
        const taskResult = await client.query(`
          INSERT INTO tasks (
            project_id, title, description, status, priority, 
            due_date, created_by, assigned_to, created_at, updated_at
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())
          RETURNING id, project_id, title, description, status, priority, 
                   due_date, created_by, assigned_to, created_at, updated_at
        `, [
          projectId,
          title.trim(),
          description.trim(),
          status,
          priority,
          dueDate || null,
          userId,
          assignedTo || null
        ]);

        console.log('✅ Task inserted, result:', taskResult.rows[0]);

        await client.query('COMMIT');
        console.log('✅ Transaction committed');

        const row = taskResult.rows[0];
        
        // Convert snake_case database fields to camelCase API response
        const task: SimpleTask = {
          id: row.id,
          projectId: row.project_id,
          title: row.title,
          description: row.description,
          status: row.status,
          priority: row.priority,
          dueDate: row.due_date,
          assignedTo: row.assigned_to,
          createdBy: row.created_by,
          createdAt: row.created_at,
          updatedAt: row.updated_at
        };

        console.log('🎉 Task created successfully:', task);
        return task;

      } catch (error) {
        await client.query('ROLLBACK');
        console.error('❌ Transaction rolled back due to error:', error);
        throw error;
      } finally {
        client.release();
        console.log('🔌 Database connection released');
      }
    } catch (error) {
      console.error('❌ Database error in createTask:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        error: error
      });
      throw new Error(`Failed to create task: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
