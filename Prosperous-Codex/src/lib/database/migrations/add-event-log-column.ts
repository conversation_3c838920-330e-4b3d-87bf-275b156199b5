import { PoolClient } from 'pg';

/**
 * Migration to add event_log column to projects table
 */
export async function addEventLogColumn(client: PoolClient) {

  try {
    // Check if column already exists
    const result = await client.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'projects' AND column_name = 'event_log'
    `);

    if (result.rows.length === 0) {
      console.log('Adding event_log column to projects table...');
      await client.query("ALTER TABLE projects ADD COLUMN event_log TEXT");
      console.log('Successfully added event_log column to projects table');
    } else {
      console.log('event_log column already exists in projects table');
    }
  } catch (error) {
    console.error('Error adding event_log column:', error);
    throw error;
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  addEventLogColumn();
}
