import { PoolClient } from 'pg';
import { addEventLogColumn } from './add-event-log-column';
import { addPerformanceIndexes } from './add-performance-indexes';

/**
 * Apply all pending migrations
 */
export async function applyMigrations(client: PoolClient) {
  console.log('🔄 Applying database migrations...');

  try {
    // Apply migrations in order
    await addEventLogColumn(client);
    await addPerformanceIndexes(client);

    console.log('✅ All migrations applied successfully');
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}
