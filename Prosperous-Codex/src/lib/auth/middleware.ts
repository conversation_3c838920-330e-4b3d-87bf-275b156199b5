import { NextRequest } from 'next/server';
import { auth } from '@/auth';
import { User } from '@/lib/types/auth';

// Simple in-memory cache for user verification (5 minute TTL)
export const userVerificationCache = new Map<string, { user: User; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Authenticate request and return user if valid using NextAuth.js with caching
 */
export async function authenticateRequest(request: NextRequest): Promise<User | null> {
  try {
    const session = await auth();

    if (!session?.user?.email) {
      return null;
    }

    const email = session.user.email;
    const now = Date.now();

    // Check cache first
    const cached = userVerificationCache.get(email);
    if (cached && (now - cached.timestamp) < CACHE_TTL) {
      return cached.user;
    }

    // Verify that the user still exists in the database
    const { UserService } = await import('@/lib/database/user-service');
    const userService = new UserService();
    const dbUser = userService.getUserByEmail(email);

    if (!dbUser) {
      console.log('User from session not found in database, session invalid');
      // Remove from cache if user no longer exists
      userVerificationCache.delete(email);
      return null;
    }

    // Convert NextAuth.js user to our User type using the current database user
    const user: User = {
      id: dbUser.id.toString(),
      email: dbUser.email,
      username: dbUser.username,
      role: dbUser.role,
    };

    // Cache the result
    userVerificationCache.set(email, { user, timestamp: now });

    // Clean up expired cache entries periodically
    if (userVerificationCache.size > 100) {
      for (const [key, value] of userVerificationCache.entries()) {
        if ((now - value.timestamp) >= CACHE_TTL) {
          userVerificationCache.delete(key);
        }
      }
    }

    return user;
  } catch (error) {
    console.error('Authentication error:', error);
    return null;
  }
}

/**
 * Check if user has required role
 */
export function hasRole(user: User | null, requiredRole: 'user' | 'moderator' | 'admin'): boolean {
  if (!user || !user.role) {
    return false;
  }
  
  const roleHierarchy = {
    'user': 1,
    'moderator': 2,
    'admin': 3
  };
  
  const userLevel = roleHierarchy[user.role as keyof typeof roleHierarchy] || 0;
  const requiredLevel = roleHierarchy[requiredRole];
  
  return userLevel >= requiredLevel;
}

/**
 * Require authentication for API routes
 */
export async function requireAuth(request: NextRequest): Promise<{ user: User } | { error: string; status: number }> {
  const user = await authenticateRequest(request);
  
  if (!user) {
    return { error: 'Authentication required', status: 401 };
  }
  
  return { user };
}

/**
 * Require specific role for API routes
 */
export async function requireRole(
  request: NextRequest, 
  requiredRole: 'user' | 'moderator' | 'admin'
): Promise<{ user: User } | { error: string; status: number }> {
  const authResult = await requireAuth(request);
  
  if ('error' in authResult) {
    return authResult;
  }
  
  if (!hasRole(authResult.user, requiredRole)) {
    return { error: 'Insufficient permissions', status: 403 };
  }
  
  return authResult;
}

/**
 * Create session token and set cookie
 */
export function createSessionResponse(sessionToken: string, rememberMe: boolean = false) {
  const maxAge = rememberMe ? 7 * 24 * 60 * 60 : 24 * 60 * 60; // 7 days or 24 hours
  
  return {
    sessionToken,
    cookieOptions: {
      name: 'session-token',
      value: sessionToken,
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      maxAge,
      path: '/'
    }
  };
}

/**
 * Clear session cookie
 */
export function clearSessionResponse() {
  return {
    cookieOptions: {
      name: 'session-token',
      value: '',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      maxAge: 0,
      path: '/'
    }
  };
}

/**
 * Utility to get current user from request (for use in API routes)
 */
export async function getCurrentUser(request: NextRequest): Promise<User | null> {
  return await authenticateRequest(request);
}

/**
 * Utility to check if request is from admin
 */
export async function isAdmin(request: NextRequest): Promise<boolean> {
  const user = await authenticateRequest(request);
  return hasRole(user, 'admin');
}

/**
 * Utility to check if request is from moderator or admin
 */
export async function isModerator(request: NextRequest): Promise<boolean> {
  const user = await authenticateRequest(request);
  return hasRole(user, 'moderator');
}
